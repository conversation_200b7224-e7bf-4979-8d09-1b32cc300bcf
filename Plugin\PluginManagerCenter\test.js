const PluginManagerCenter = require('./PluginManagerCenter');
const fs = require('fs').promises;
const path = require('path');

class PluginManagerTester {
    constructor() {
        this.manager = new PluginManagerCenter();
        this.testResults = [];
    }

    async runTest(testName, testFunction) {
        console.log(`\n🧪 Running test: ${testName}`);
        try {
            const startTime = Date.now();
            const result = await testFunction();
            const duration = Date.now() - startTime;
            
            console.log(`✅ ${testName} passed (${duration}ms)`);
            this.testResults.push({ name: testName, status: 'passed', duration });
            return result;
        } catch (error) {
            console.log(`❌ ${testName} failed: ${error.message}`);
            this.testResults.push({ name: testName, status: 'failed', error: error.message });
            throw error;
        }
    }

    async testListPlugins() {
        return this.runTest('List Plugins', async () => {
            const result = await this.manager.processRequest({
                command: 'list_plugins',
                filter: 'all',
                detailed: 'false'
            });

            if (result.status !== 'success') {
                throw new Error(`Expected success, got ${result.status}`);
            }

            if (!result.result || typeof result.result.total !== 'number') {
                throw new Error('Invalid result structure');
            }

            console.log(`   Found ${result.result.total} plugins`);
            return result;
        });
    }

    async testListPluginsDetailed() {
        return this.runTest('List Plugins (Detailed)', async () => {
            const result = await this.manager.processRequest({
                command: 'list_plugins',
                filter: 'all',
                detailed: 'true'
            });

            if (result.status !== 'success') {
                throw new Error(`Expected success, got ${result.status}`);
            }

            const plugins = result.result.plugins;
            if (!Array.isArray(plugins)) {
                throw new Error('Plugins should be an array');
            }

            if (plugins.length > 0) {
                const firstPlugin = plugins[0];
                const requiredFields = ['name', 'displayName', 'version', 'status'];
                for (const field of requiredFields) {
                    if (!(field in firstPlugin)) {
                        throw new Error(`Missing required field: ${field}`);
                    }
                }
            }

            console.log(`   Detailed info for ${plugins.length} plugins`);
            return result;
        });
    }

    async testPluginStatus() {
        return this.runTest('Plugin Status', async () => {
            // 首先获取插件列表
            const listResult = await this.manager.processRequest({
                command: 'list_plugins',
                filter: 'all',
                detailed: 'false'
            });

            if (listResult.result.plugins.length === 0) {
                console.log('   No plugins found, skipping status test');
                return { status: 'success', result: { message: 'No plugins to test' } };
            }

            const firstPlugin = listResult.result.plugins[0];
            const result = await this.manager.processRequest({
                command: 'plugin_status',
                plugin_name: firstPlugin.name
            });

            if (result.status !== 'success') {
                throw new Error(`Expected success, got ${result.status}`);
            }

            if (!result.result || result.result.name !== firstPlugin.name) {
                throw new Error('Invalid plugin status result');
            }

            console.log(`   Status for plugin: ${firstPlugin.name}`);
            return result;
        });
    }

    async testCheckDependencies() {
        return this.runTest('Check Dependencies', async () => {
            const result = await this.manager.processRequest({
                command: 'check_dependencies'
            });

            if (result.status !== 'success') {
                throw new Error(`Expected success, got ${result.status}`);
            }

            if (!result.result || typeof result.result !== 'object') {
                throw new Error('Invalid dependencies result');
            }

            const pluginCount = Object.keys(result.result).length;
            console.log(`   Checked dependencies for ${pluginCount} plugins`);
            return result;
        });
    }

    async testPluginStats() {
        return this.runTest('Plugin Stats', async () => {
            const result = await this.manager.processRequest({
                command: 'plugin_stats',
                time_range: '7d'
            });

            if (result.status !== 'success') {
                throw new Error(`Expected success, got ${result.status}`);
            }

            if (!result.result || typeof result.result !== 'object') {
                throw new Error('Invalid stats result');
            }

            console.log(`   Retrieved stats for all plugins`);
            return result;
        });
    }

    async testTogglePlugin() {
        return this.runTest('Toggle Plugin', async () => {
            // 获取第一个插件进行测试
            const listResult = await this.manager.processRequest({
                command: 'list_plugins',
                filter: 'all',
                detailed: 'false'
            });

            if (listResult.result.plugins.length === 0) {
                console.log('   No plugins found, skipping toggle test');
                return { status: 'success', result: { message: 'No plugins to test' } };
            }

            const firstPlugin = listResult.result.plugins[0];
            
            // 禁用插件
            const disableResult = await this.manager.processRequest({
                command: 'toggle_plugin',
                plugin_name: firstPlugin.name,
                action: 'disable'
            });

            if (disableResult.status !== 'success') {
                throw new Error(`Failed to disable plugin: ${disableResult.error}`);
            }

            // 重新启用插件
            const enableResult = await this.manager.processRequest({
                command: 'toggle_plugin',
                plugin_name: firstPlugin.name,
                action: 'enable'
            });

            if (enableResult.status !== 'success') {
                throw new Error(`Failed to enable plugin: ${enableResult.error}`);
            }

            console.log(`   Successfully toggled plugin: ${firstPlugin.name}`);
            return enableResult;
        });
    }

    async testInvalidCommand() {
        return this.runTest('Invalid Command', async () => {
            const result = await this.manager.processRequest({
                command: 'invalid_command'
            });

            if (result.status !== 'error') {
                throw new Error(`Expected error, got ${result.status}`);
            }

            console.log(`   Correctly handled invalid command`);
            return result;
        });
    }

    async runAllTests() {
        console.log('🚀 Starting PluginManagerCenter Tests\n');
        
        const tests = [
            () => this.testListPlugins(),
            () => this.testListPluginsDetailed(),
            () => this.testPluginStatus(),
            () => this.testCheckDependencies(),
            () => this.testPluginStats(),
            () => this.testTogglePlugin(),
            () => this.testInvalidCommand()
        ];

        let passed = 0;
        let failed = 0;

        for (const test of tests) {
            try {
                await test();
                passed++;
            } catch (error) {
                failed++;
            }
        }

        console.log('\n📊 Test Results Summary:');
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => r.status === 'failed')
                .forEach(r => console.log(`   - ${r.name}: ${r.error}`));
        }

        return { passed, failed, total: passed + failed };
    }
}

// 运行测试
async function main() {
    const tester = new PluginManagerTester();
    try {
        await tester.runAllTests();
        process.exit(0);
    } catch (error) {
        console.error('Test runner failed:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
