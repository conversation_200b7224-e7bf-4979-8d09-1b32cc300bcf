{"manifestVersion": "1.0.0", "name": "FileServer", "version": "1.0.0", "displayName": "文件服务", "description": "提供受密码保护的静态文件服务。", "author": "Kilo Code", "pluginType": "service", "entryPoint": {"type": "nodejs", "script": "file-server.js"}, "communication": {"protocol": "direct"}, "configSchema": {"File_Key": "string", "DebugMode": "boolean"}, "capabilities": {"services": [{"serviceName": "ProtectedFileHosting", "description": "通过 /pw=[File_Key]/files/... 路径提供文件服务。"}]}}