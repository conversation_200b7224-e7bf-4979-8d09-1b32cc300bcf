[{"doi": "10.30574/wjaets.2025.16.1.1200", "title": "Scalable MLOPS for in-game AI Features: From highlight detection to player behavior modeling", "authors": "<PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "The integration of artificial intelligence (AI) in modern gaming has enabled dynamic and personalized in-game experiences, including real-time highlight detection and adaptive player behavior modeling. Central to operationalizing these AI features is the application of machine learning operations (MLOPS)—a framework that streamlines model development, deployment, and monitoring at scale. This review synthesizes current methodologies across deep learning, reinforcement learning, and imitation learning in the gaming context, highlighting the role of MLOPS in ensuring system robustness and scalability. Experimental results show the superiority of transformer architectures for highlight detection and behavior cloning methods for imitation learning. We also discuss operational bottlenecks, ethical considerations, and propose future directions including meta-learning, federated training, and energy-efficient AI infrastructures. This paper aims to serve as a comprehensive reference for researchers and practitioners in gaming AI and scalable MLOPS systems.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Engineering Technology and Sciences"}, {"doi": "10.30574/wjbphs.2025.23.1.0667", "title": "Triptorelin acetate in pediatric endocrinology: A retrospective analysis of treatment outcomes and long-term efficacy", "authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "issuedDate": "2025-7-30", "abstract": "Aims: This study aims to evaluate triptorelin acetate's efficacy in suppressing pubertal progression and stabilizing bone age advancement in Jordanian children with Central Precocious Puberty (CPP). It will also monitor regression of secondary sexual characteristics, evaluate bone age progression relative to chronological age, and assess improvements in predicted adult height. The study will provide insights into the real-world effectiveness of triptorelin acetate in a Middle Eastern pediatric population, guiding clinicians in tailoring treatment protocols and stratifying patients for optimal response. The findings may also inform future monitoring and follow-up strategies for children undergoing GnRH agonist therapy.\nMethods: The study analyzed the medical records of 79 children treated with medication at Queen Rania Abdallah Hospital for Children from January 2023 to December 2024. The children had to meet certain criteria, including being at least 13 years old, having a confirmed diagnosis of CPP, and having complete baseline and follow-up data. The data set included information about age, gender, and BMI, as well as clinical parameters like growth rate and pubertal staging. Hormonal tests were conducted to monitor puberty progression, and the Greulich-Pyle approach was used to evaluate bone age. Written information about the therapy was also collected. Statistical analysis was performed to identify useful information, using means, rates, paired t-tests, Wilcoxon signed-rank tests, multivariate regression analysis, and ROC curve analysis. A power of 80% was used to ensure clinically meaningful effects, and a p-value of less than 0.05 was considered statistically significant. The data was stored using conventional statistical programs.\nResults: A study on Central Premature Puberty in children found that triptorelin acetate effectively blocked gonadotropins, reducing levels of LH and FSH in 92.4% of patients. The treatment also decreased levels of FSH and sex steroids, indicating hormones' role in puberty regulation. Post-treatment, children's projected adult height (PAH) grew by an average of +4.2 ± 1.7 cm, and the bone age to chronological age ratio was significantly reduced. Younger age at the start and longer treatment period were significant indicators of better outcomes for PAH. The recommended cutoff age for the greatest treatment response was ≤8 years, emphasizing the need for early detection and treatment of CPP. Triptorelin acetate is generally well-tolerated with no notable side effects.\nConclusion: Research shows triptorelin acetate can slow bone ageing, prevent puberty, and improve PAH in children as young as eight years old. However, more long-term studies are needed to determine its predictive characteristics and long-term results. Despite no damage found, the current CPP recommendations for early therapy for GnRHa are more credible.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Biology Pharmacy and Health Sciences"}, {"doi": "10.30574/wjbphs.2025.23.1.0664", "title": "Advancing caries detection in dentistry: A narrative review of artificial intelligence applications", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "Artificial intelligence is rapidly transforming dental diagnostics, particularly in the detection of caries, offering unprecedented precision and efficiency compared to conventional methods. This review explores the evolution of AI applications in dentistry, highlighting how machine learning, deep learning, and computer vision are reshaping diagnostic processes. Traditional methods such as visual-tactile examinations and radiographic imaging, while fundamental, often suffer from limitations including human error, inconsistency, and difficulty in early detection. AI technologies address these challenges by offering consistent, fast, and highly accurate detection capabilities. Convolutional neural networks (CNNs) have demonstrated remarkable success in analyzing bitewing, periapical, and panoramic images, often outperforming human examiners in detecting early-stage carious lesions. Beyond radiographic analysis, AI-driven image segmentation enhances diagnostic precision by objectively highlighting affected regions, supporting clinicians in devising more tailored treatment strategies. Clinical applications already show that AI not only boosts diagnostic confidence but also improves patient engagement by providing visual explanations. Despite its promising potential, the field faces hurdles such as the need for large, diverse, and high-quality datasets, concerns about data privacy, and the necessity for rigorous validation across different populations. Ethical and legal considerations, particularly around accountability and explainability, further emphasize the need for clear regulatory frameworks. Emerging trends focus on explainable AI, multidisciplinary collaborations, and personalized AI solutions that integrate with electronic dental records, paving the way for more patient-specific care. Studies to date show that AI models can achieve caries detection accuracies exceeding 80%, with some nearing 99%, demonstrating the immense future promise of this technology. However, unlocking AI’s full potential in dentistry will require ongoing research, validation in real-world settings, and a concerted effort between dental professionals, AI developers, and regulators to ensure that AI systems are safe, reliable, and ethically implemented to enhance patient outcomes and revolutionize dental care.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Biology Pharmacy and Health Sciences"}, {"doi": "10.30574/wjarr.2025.27.1.2551", "title": "AI-driven image classification for early detection of crop diseases", "authors": "<PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "Crop diseases pose a significant threat to agricultural productivity and food security. Early detection is essential for effective disease management and timely intervention. However, the limitations of human vision often lead to delayed identification, typically after the disease has already caused considerable damage. To address this challenge, we present a custom-built Convolutional Neural Network (CNN) model designed to accelerate and improve the accuracy of plant disease detection. Our model was thoroughly trained and evaluated using a variety of datasets featuring apple, corn, and tomato crops, sourced primarily from platforms like Kaggle. Unlike conventional classification models that are often tailored to specific datasets, our model is designed to handle images taken under diverse lighting conditions, orientations, and resolutions, making it adaptable to a wide range of real-world farm environments. Through a structured training and validation process, our CNN consistently achieved testing accuracies of between 97% and 99% across all datasets. These results significantly outperform many existing CNN-based approaches to crop disease detection. The broader implications of this work are substantial for agriculture and crop management. By integrating our AI-powered detection system, we not only tackle immediate agricultural challenges but also contribute to addressing global concerns such as food insecurity and environmental sustainability. Early disease detection using our model aids in minimizing crop losses and optimizing resource usage, thereby supporting the growing demands of a rising global population and mitigating the effects of environmental stress on food systems. Nonetheless, it is important to recognize that, as a classification model, it may exhibit reduced accuracy when analyzing images that include unrelated visual elements. Overall, this research highlights the pivotal role that AI-based technologies can play in strengthening agricultural resilience and advancing global food security.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.59463/rjvs.2025.2.17", "title": "The microbial landscape of donkey milk: a systematic review", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "issuedDate": "2025-7-30", "abstract": "Donkey milk has gained considerable attention in recent years due to its distinctive nutritional profile and potential health benefits, especially for vulnerable populations such as infants and individuals who cannot tolerate cow milk. This systematic review aims to assess the microbiological characteristics of raw donkey milk by examining its overall microbial composition through a broad evaluation of the existing scientific literature. An extensive literature search was conducted for studies published from January 2010 to October 2024, utilizing databases such as PubMed, Scopus, and Web of Science. The search strategy incorporated keywords including \"donkey milk,\" \"microbiological status,\" \"microbial composition,\" and \" microbiological quality\". Inclusion criteria were strictly limited to original research studies providing empirical data on the microbiological composition of raw donkey milk. The review reveals that donkey milk primarily contains beneficial lactic acid bacteria (LAB), such as Lactobacillus and Streptococcus, highlighting its probiotic potential. While some studies reported the presence of opportunistic or spoilage-related bacteria, these were typically found at low levels and under specific conditions. Overall, the findings support the microbiological integrity of donkey milk when hygienic practices are followed. Future research should aim to further characterize its microbial ecology, standardize analytical approaches, and support safe production and storage protocols to promote its development as a functional dairy product.", "publisher": "Universitatea de Stiintele Vietii Regele Mihai I din Timisoara", "type": "journal-article", "journal": "Romanian Journal of Veterinary Sciences"}, {"doi": "10.35940/ijrte.b8262.14020725", "title": "Forensic Analysis of Deepfake Audio Detection", "authors": "Dr. <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "The rise of deepfake audio technologies poses significant challenges to authenticity verification, necessitating effective detection methods. Traditional techniques, such as manual forensic analysis, basic machine learning approaches, speech-to-text conversion, and Short-Time Fourier Transform (STFT) analysis, have been employed to identify manipulated audio. However, these methods often fall short due to their time-consuming nature, inability to handle complex sequential data, and susceptibility to high-quality synthetic audio. This paper presents an innovative approach that leverages Long Short-Term Memory (LSTM) networks and Mel-Frequency Cepstral Coefficients (MFCC) for deepfake audio detection. By harnessing the power of deep learning, LSTMs can effectively capture temporal dependencies in audio data, allowing for the identification of subtle anomalies that indicate manipulation. The use of MFCC enables the extraction of robust audio features that align closely with human auditory perception, thereby enhancing the models sensitivity to synthetic alterations. Additionally, our methodology incorporates enhanced preprocessing techniques to ensure high-quality input data, thereby further improving detection accuracy. The proposed system demonstrates a significant advancement in deepfake audio detection, providing a more reliable solution against increasingly sophisticated audio manipulations.", "publisher": "Blue Eyes Intelligence Engineering and Sciences Engineering and Sciences Publication - BEIESP", "type": "journal-article", "journal": "International Journal of Recent Technology and Engineering (IJRTE)"}, {"doi": "10.30574/wjarr.2025.27.1.2463", "title": "Integration of AI with ethical hacking tools for predictive vulnerability detection", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "The evolving nature of cyber threats, especially zero-day exploits, demands a shift from traditional reactive security mechanisms to proactive and predictive defense strategies. This paper explores the integration of Artificial Intelligence (AI) with ethical hacking tools to enhance predictive vulnerability detection, focusing on Snort and Maltego. By embedding machine learning algorithms into these tools, their capabilities in anomaly detection and threat intelligence are significantly enhanced. This research investigates the integration of machine learning (ML) algorithms into ethical hacking tools, Snort and Maltego to strengthen their anomaly detection and threat intelligence functionalities. This study presents AI-driven framework where supervised and unsupervised learning models are embedded into Snort for packet level anomaly detection and into Maltego for enhanced threat correlation. Applying machine learning algorithms to detect and classify threats based on data from live network traffic and threat intelligence sources. Training and evaluation methods are used to improve accuracy and reduce false alarms. Although challenges like data labelling, changing patterns, and ethical issues exist, this approach greatly strengthens early threat detection and response. This research supports the advancement of intelligent cybersecurity systems capable of proactive threat mitigation.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.47760/ijcsmc.2025.v14i07.003", "title": "Real-Time Image Processing for Defect Detection in Laser Powder Bed Fusion Using Machine Learning", "authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "issuedDate": "2025-7-30", "abstract": "Laser powder bed fusion (LPBF) is a key additive manufacturing technique used in producing high-precision metal components. However, quality assurance in LPBF is challenged by defects resulting from poor powder spread-ability. This paper presents a machine learning-based system for real-time defect detection in laser powder bed fusion (LPBF) additive manufacturing. The study evaluates three deep learning architectures i.e YOLOv8, Mask R-CNN, and U-Net, for identifying and classifying powder bed defects, including re-coater hopping, streaking, incomplete spreading, and debris. The system achieves a mean Average Precision (mAP) of 71.73% with Mask R-CNN, demonstrating its potential to replace manual inspection in resource-constrained settings like Zimbabwe. A web-based decision support system provides actionable recommendations, reducing material waste and improving manufacturing efficiency.", "publisher": "Zain Publications", "type": "journal-article", "journal": "International Journal of Computer Science and Mobile Computing"}, {"doi": "10.30574/wjarr.2025.27.1.2655", "title": "AI-based threat detection in critical infrastructure: A case study on smart grids", "authors": "<PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "issuedDate": "2025-7-30", "abstract": "The modernization of electrical power systems through smart grid technologies has introduced unprecedented opportunities for enhanced efficiency, reliability, and sustainability. However, this digital transformation has also expanded the attack surface for cyber threats, making critical infrastructure increasingly vulnerable to sophisticated cyberattacks. This paper examines the application of artificial intelligence (AI) and machine learning (ML) technologies for threat detection in smart grid systems within the United States context. Through a comprehensive analysis of current deployment scenarios, threat landscapes, and AI-driven security frameworks, this study demonstrates how intelligent systems can enhance the resilience of critical infrastructure. The research presents empirical data from major U.S. utilities, evaluates the effectiveness of various AI algorithms in detecting anomalous behavior, and provides recommendations for implementing robust AI-based security solutions in smart grid environments.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.5056/jnm25024", "title": "Long-term Effects of Potassium-competitive Acid Blockers and Proton Pump Inhibitors on Gastrin, Gastric Emptying Rate, and Small Intestinal Microbiota in Rats", "authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "issuedDate": "2025-7-30", "abstract": "N/A", "publisher": "The Korean Society of Neurogastroenterology and Motility", "type": "journal-article", "journal": "Journal of Neurogastroenterology and Motility"}, {"doi": "10.53771/ijlsra.2025.9.1.0045", "title": "Unraveling tumor microenvironments with spatial genomics: Implications for precision breast cancer therapy", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "Spatial genomics has revolutionized breast cancer research by mapping gene expression within the tumor microenvironment, offering unprecedented insights into the molecular and cellular interactions that drive tumor progression and therapeutic response across luminal A, luminal B, HER2-positive, and triple-negative subtypes. Unlike traditional genomic methods, spatial genomics preserves tissue architecture, revealing subtype-specific TME features, such as immune-rich regions in triple-negative breast cancer and stromal-dense environments in HER2-positive tumors. This review explores spatial genomics technologies, including Visium, GeoMx, and MERFISH, which enable precise TME analysis, highlighting their role in identifying biomarkers like PD-L1 and TGF-β for targeted therapies and immunotherapies. By elucidating TME-driven resistance mechanisms, such as hypoxia in triple-negative tumors or stromal remodeling in HER2-positive cases, spatial genomics informs clinical trials and combination therapies, enhancing patient stratification and treatment efficacy. Despite its promise, challenges including limited resolution, complex data analysis, and high costs hinder clinical adoption. Emerging technologies, artificial intelligence, and multi-omics integration offer solutions, promising higher precision and scalability. This review underscores spatial genomics’ transformative potential to bridge research and clinical practice, paving the way for personalized breast cancer therapies tailored to the dynamic TME, with future advancements poised to redefine treatment paradigms and improve patient outcomes.", "publisher": "Scientific Research Archives", "type": "journal-article", "journal": "International Journal of Life Science Research Archive"}, {"doi": "10.30574/ijsra.2025.16.1.1970", "title": "Multi-cloud data platforms for real-time fraud detection and prevention", "authors": "<PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "In today’s fast-paced digital world, fraud detection stands out as a key area of both academic interest and real-world development—particularly as businesses increasingly depend on multi-cloud setups. This review explores how AI helps power those real-time defenses. It unpacks the core architectural elements, AI and machine learning approaches, and real-world metrics drawn from academic literature. A theoretical model is proposed that supports scale and privacy compliance, using stream processing and distributed learning. Experiments show that tools like XG Boost, LSTM, and Federated Learning work well in live, multi-cloud setups. The review also points to important research gaps and lays out possible next steps to improve fraud detection’s flexibility, ethical grounding, and long-term resilience across cloud systems.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "International Journal of Science and Research Archive"}, {"doi": "10.30574/ijsra.2025.16.1.2070", "title": "Energizing blockchain and Artificial Intelligence for enhanced fraud detection in modern banking systems", "authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>; Apur<PERSON>", "issuedDate": "2025-7-30", "abstract": "This paper explores the synergistic integration of blockchain and Artificial Intelligence (AI) to enhance fraud detection in modern banking systems. By leveraging blockchain’s immutable ledger and AI’s advanced pattern recognition capabilities, financial institutions can achieve real-time, transparent, and accurate identification of fraudulent activities. This integration not only strengthens security and trust but also addresses challenges related to data privacy, regulatory compliance, and evolving fraud tactics. The study highlights the transformative potential of combining these technologies to create resilient and adaptive fraud prevention frameworks in the banking sector.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "International Journal of Science and Research Archive"}, {"doi": "10.29013/ajt-25-5.6-42-51", "title": "Optimized Microwave-Assisted Synthesis Of Dithizone-Doped Chitosan Carbon Dots For  Enhanced Photoluminescence And Lead(Ii) Ion Detection", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "N/A", "publisher": "Premier Publishing s.r.o.", "type": "journal-article", "journal": "Austrian Journal of Technical and Natural Sciences"}, {"doi": "10.30574/gjeta.2025.24.1.0224", "title": "Optimization of a web-based real-time and file-based human detection system Using YOLOv8 and flask framework", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "This study discusses the development and optimization of an object detection system using the YOLOv8 (You Only Look Once version 8) algorithm integrated with the Flask framework in a web-based environment. The system supports two main modes of operation, namely image upload and direct detection using the camera. The main goal of the system is to provide accurate, efficient, and accessible solutions without the need for local installation. The test was conducted using 50 test images with different variations in background, lighting conditions, and the number of human objects, resulting in an average inference time of 0.43 seconds per image, precision of 95.1%, recall of 91.7%, and mAP@0.5 of 93.4%. In real-time testing, the system was able to run stably with a video processing speed of 18-22 frames per second. These results show that the developed system has high performance and is feasible to apply for online visual monitoring needs. Potential system development includes the addition of object tracking features, automatic log storage integration, and real-time notification systems to expand usability in various fields.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "Global Journal of Engineering and Technology Advances"}, {"doi": "10.30574/ijsra.2025.16.1.2047", "title": "Ransomware Attack Detection: Developing machine learning-based detection models", "authors": "<PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "Today's cybersecurity infrastructure faces a significant difficulty due to the rise and development of ransomware attacks. Typically, antivirus tools that use signatures cannot identify new and fast-changing ransomware, so changes in detection are required. The piece looks at how machine learning can be used to spot ransomware during attacks. This method relies on feature engineering, where relevant details are removed and picked out from masses of activity, files, and traffic seen on the computer. Both static and dynamic features help identify whether a system is infected with ransomware before any payload is launched. Many machine learning algorithms are studied to find out if they can help model the actions of complex ransomware. Addressing model evaluation metrics such as precision, recall, F1-score, and ROC-AUC explains the limitations of using models in practice. This means the models must quickly identify threats and avoid mistakenly reporting them as false alarms in the real world.\n\nFurthermore, the article mentions issues related to skewed data, bypassing defenses, and growing systems in applications used in real-time. Using models that apply machine learning technology, businesses can enhance their response to threats. Therefore, organizations are prepared to face new ransomware attacks using information from the data they protect.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "International Journal of Science and Research Archive"}, {"doi": "10.30574/wjarr.2025.27.1.2732", "title": "AI-powered road damage detection for enhanced safety and life protection", "authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "Road condition assessment is one of the leading concerns for the sustainability of road safety and condition, and hence has a direct effect on transportation efficiency, particularly for older infrastructures. The road diseases of the traditional roads are manual detection and recognition. This method is slow, has high costs, and is subject to personal subjective effect factors. In this paper, we propose employing the Vision Transformer (ViT) with lightweight CNN encoders for damage detection. It was prepared and tested on a dataset of 10,000 road images. The objects in the scene may also change smoothly from one to another, so we use the Vision Transformer because it can be a more powerful model to capture the global dependencies, as well as more complex attributes of the scene, and also the images (where, with high probability, precise classification is demanded). An experiment in the research study demonstrated that the new model could be used to detect road damage with an accuracy of 94%. An automatic evaluation can be carried out hundreds of times faster than a manual evaluation and is infinitely more reliable than manual scoring. AI-driven infrastructure monitoring that supports vehicle safety, making them serviced on time and reducing operational costs. The model can also be applied to various potential applications, including the development of a more effective road management system and the financing of maintenance, conservation, and road network expansion.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.30574/wjarr.2025.27.1.2651", "title": "Privacy-preserving detection of encrypted AI traffic in IoT using lightweight flow-level machine learning", "authors": "<PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "The widespread integration of AI-driven services into IoT ecosystems introduces pressing cybersecurity and traffic visibility challenges—particularly in the presence of encrypted, low-latency protocols such as WebSocket Secure (WSS) and Model Context Protocol (MCP) over HTTPS. Traditional Deep Packet Inspection (DPI) techniques are rendered ineffective due to encryption, and payload-dependence is increasingly impractical amid growing privacy and regulatory constraints. This study presents a novel, technically robust, and scalable machine learning framework that classifies AI-generated traffic using only flow-level metadata. By leveraging transport-layer characteristics such as session duration and directional byte counts, this method achieves high F1 scores across encrypted and unencrypted WebSocket traffic, and perfect accuracy in classifying MCP streams. The framework is evaluated across multiple traffic scenarios using Random Forest and Logistic Regression models, yielding F1 scores exceeding 0.97 for WebSockets and 0.99 for MCP. Designed for efficiency, the system executes with sub-5ms inference latency on edge-grade devices, making it ideal for real-time IoT deployments. This work addresses a critical visibility gap in encrypted AI communications and contributes a privacy-preserving, protocol-agnostic approach to next-generation traffic classification in smart environments.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.30574/wjarr.2025.27.1.2541", "title": "Enhancing malware detection using federated learning and explainable AI for privacy-preserving threat intelligence", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "The escalating complexity and frequency of malware attacks pose a significant challenge to conventional cybersecurity frameworks, particularly in scenarios demanding high data privacy and cross-organizational threat intelligence sharing. Traditional centralized machine learning models for malware detection often rely on aggregating data in a central server, thereby increasing the risk of data breaches and limiting the deployment of models in privacy-sensitive environments such as healthcare, finance, and critical infrastructure. To address these limitations, this study explores an integrated approach that combines Federated Learning (FL) with Explainable Artificial Intelligence (XAI) for enhancing malware detection while preserving user privacy and system confidentiality. Federated learning enables the collaborative training of robust malware classifiers across multiple decentralized nodes without sharing raw data, thus maintaining local data sovereignty and complying with data protection regulations. The proposed framework incorporates deep learning architectures such as convolutional neural networks (CNNs) trained in a federated environment using feature vectors extracted from malicious binaries and behavior logs. To ensure transparency and trust in model predictions, explainable AI techniques specifically SHAP (SHapley Additive exPlanations) and LIME (Local Interpretable Model-agnostic Explanations) are integrated, providing actionable insights into the model’s decision-making process. This study also presents a comprehensive evaluation using a benchmark malware dataset distributed across simulated client environments, measuring detection accuracy, communication overhead, privacy leakage, and interpretability performance. Results demonstrate that the FL-XAI approach achieves detection rates comparable to centralized models while ensuring data confidentiality and interpretability. The research contributes to the evolving field of privacy-preserving threat intelligence by offering a scalable and explainable framework suitable for real-time cybersecurity applications.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.47760/cognizance.2025.v05i07.015", "title": "Oil and Water Tool Detection Sensor", "authors": "<PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>", "issuedDate": "2025-7-30", "abstract": "This study addresses the critical issue of oil contamination in engine coolant systems, a prevalent problem in internal combustion engines that leads to severe damage and costly repairs. Current detection methods are often inadequate for early identification. We propose and develop a novel sensor tool designed for real-time detection of oil presence within the coolant. Developed using the ADDIE framework, the prototype integrates an Arduino, turbidity sensor, LCD display, and an alarm system to monitor coolant quality. This tool aims to provide immediate alerts for oil-coolant mixing, thereby enabling prompt preventative action and enhancing engine longevity and maintenance practices.", "publisher": "Zain Publications", "type": "journal-article", "journal": "Cognizance Journal of Multidisciplinary Studies"}, {"doi": "10.30574/wjarr.2025.27.1.2501", "title": "A novel deep learning-based method for vehicle model and number plate detection in camera-captured blurred video using YOLOv5, EasyOCR, and ResNet50", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-7-30", "abstract": "This research presents a deep learning-based system for vehicle identification, combining Vehicle Make and Model Recognition (VMMR) with Automatic Number Plate Recognition (ANPR). Unlike traditional methods that handle each task separately, the integrated approach offers a more efficient and reliable solution, even in challenging weather conditions. The system utilizes MobileNet-V2, YOLOx, YOLOv4-tiny, Paddle OCR, and SVTR-tiny, and is tested on diverse real-world images. Additionally, we have successfully handled blurred inputs captured from video and live camera streams, enhancing the system’s robustness in real-time scenarios. Results show robust performance, with further insights gained through Grad Cam technology to improve accuracy. The study’s findings have significant implications for applications in autonomous driving, traffic management, and security enforcement.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}, {"doi": "10.30574/wjarr.2025.27.1.2609", "title": "Enhancing stroke diagnosis and detection through Artificial Intelligence", "authors": "<PERSON>", "issuedDate": "2025-7-30", "abstract": "Stroke remains one of the most significant health concerns in the world that not only results in deaths but also in disabilities and the earlier a patient is diagnosed and treated, the better are the outcomes. Machine learning (ML) and deep learning (DL) are the components of Artificial Intelligence (AI) that have not yet reached their full potential in enhancing the diagnosis of the stroke because of gradually emerging medical applications. In the review, the functioning of AI technologies in stroke care was investigated with the approach to medical imaging methods as well as clinical decision support systems/symptom recognition tools and predictive models as concerns electronic health records (EHR). AI-enhanced medical imaging instruments have a high rate of ischemic and hemorrhagic stroke recognition, as well as the large vessel occlusion (and the volume of infarct core and penumbra). The same is true of medical imaging tools that can match the capacity of expert radiologists. The mobile health applications along with wearable devices are associated with real-time symptom monitoring that ensures early health intervention especially to patients who reside in isolate or underprivileged settings. The advantages of fastness, accuracy, and distant accessibility are continuously undermined by issues of bias in algorithms, along with the data quality, and also clinical integration and regulatory clearance procedures. AI holds significant promise in changing how stroke is diagnosed and treated but there is still a long way to get there and that will entail an ethical application and a powerful validation and that includes working jointly with practitioners and researchers and policymakers on behalf of an evenhanded and successful outcome.", "publisher": "GSC Online Press", "type": "journal-article", "journal": "World Journal of Advanced Research and Reviews"}]