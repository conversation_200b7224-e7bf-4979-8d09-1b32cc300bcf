{"name": "plugin-manager-center", "version": "1.0.0", "description": "VCPToolBox插件管理中心 - 集中管理插件状态、配置和性能监控", "main": "PluginManagerCenter.js", "scripts": {"start": "node PluginManagerCenter.js", "test": "node test.js", "lint": "eslint *.js", "format": "prettier --write *.js"}, "keywords": ["vcp", "plugin", "manager", "monitoring", "toolbox"], "author": "VCP Developer", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {"fs": "^0.0.1-security", "path": "^0.12.7"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/vcptoolbox"}, "bugs": {"url": "https://github.com/your-repo/vcptoolbox/issues"}, "homepage": "https://github.com/your-repo/vcptoolbox#readme"}