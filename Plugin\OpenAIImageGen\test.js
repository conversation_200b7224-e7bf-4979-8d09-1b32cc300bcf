// 测试 OpenAI 图像生成插件
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 测试配置
const testConfig = {
    OPENAI_API_KEY: "your_test_api_key_here",
    OPENAI_API_URL: "https://api.openai.com",
    OPENAI_IMAGE_MODEL: "dall-e-3",
    DEFAULT_IMAGE_SIZE: "1024x1024",
    DEFAULT_IMAGE_QUALITY: "standard",
    DEFAULT_IMAGE_STYLE: "vivid",
    MAX_RETRIES: 3,
    SAVE_IMAGES_LOCALLY: "true",
    LOCAL_SAVE_PATH: "test_images",
    DebugMode: "true"
};

// 测试数据
const testInput = {
    config: testConfig,
    prompt: "一只可爱的橘猫坐在樱花树下，春天的阳光透过花瓣洒在猫咪身上，温馨治愈的画面",
    size: "1024x1024",
    quality: "standard",
    style: "vivid",
    n: 1
};

console.log('🧪 开始测试 OpenAI 图像生成插件...\n');

// 运行插件
const child = spawn('node', ['OpenAIImageGen.js'], {
    stdio: ['pipe', 'pipe', 'pipe']
});

// 发送测试数据
child.stdin.write(JSON.stringify(testInput));
child.stdin.end();

// 收集输出
let stdout = '';
let stderr = '';

child.stdout.on('data', (data) => {
    stdout += data.toString();
});

child.stderr.on('data', (data) => {
    stderr += data.toString();
});

child.on('close', (code) => {
    console.log(`📊 插件执行完成，退出码: ${code}\n`);
    
    if (stderr) {
        console.log('🐛 调试信息:');
        console.log(stderr);
        console.log('');
    }
    
    if (stdout) {
        console.log('📤 插件输出:');
        try {
            const result = JSON.parse(stdout);
            console.log(JSON.stringify(result, null, 2));
            
            if (result.status === 'success') {
                console.log('\n✅ 测试成功！插件正常工作。');
            } else {
                console.log('\n❌ 测试失败：', result.error);
            }
        } catch (error) {
            console.log('原始输出:', stdout);
            console.log('\n❌ 输出解析失败:', error.message);
        }
    } else {
        console.log('❌ 没有收到插件输出');
    }
});

child.on('error', (error) => {
    console.error('❌ 插件启动失败:', error.message);
});

// 设置超时
setTimeout(() => {
    if (!child.killed) {
        console.log('⏰ 测试超时，终止插件进程');
        child.kill();
    }
}, 30000); // 30秒超时
