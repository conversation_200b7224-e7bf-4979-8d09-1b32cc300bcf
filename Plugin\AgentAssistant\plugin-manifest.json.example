{"manifestVersion": "1.0.0", "name": "AgentAssistant", "displayName": "Multi-Agent Collaboration Plugin", "version": "2.0.0", "description": "Allows a primary AI or user to invoke specialized, pre-configured agents for various tasks. Supports both immediate and scheduled (time-delayed) communication. Each agent can have its own distinct system prompt and configuration.", "author": "Your Name Here", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node AgentAssistant.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"AGENT_ASSISTANT_MAX_HISTORY_ROUNDS": "integer", "AGENT_ASSISTANT_CONTEXT_TTL_HOURS": "integer"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "InvokeAgent", "description": "[AgentAssistant Tool] Use this tool to consult or request assistance from a configured agent. In your response, use the following precise format, ensuring all parameter values are enclosed in '「始」' and '「末」':\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」AgentAssistant「末」,\nagent_name:「始」(Required) The exact display name of the agent to invoke (e.g., SupportAgent, SalesAgent). Refer to the agent list in your system configuration.「末」,\nprompt:「始」(Required) The question, command, or task you want the agent to handle. **Important: Please provide a brief self-introduction at the beginning of your prompt (e.g., 'I am [Your Identity/Name], and I would like you to...'),** so the agent can better understand the context of the request.「末」,\ntimely_contact:「始」(Optional) Set a future time to send this communication, in the format YYYY-MM-DD-HH:mm (e.g., 2025-12-31-18:00). If this field is provided, the communication will be scheduled for the specified time.「末」\n<<<[END_TOOL_REQUEST]>>>\n\nExample Agent Specializations (Defined in config.env):\n- SupportAgent: Technical support, troubleshooting, and product guidance.\n- SalesAgent: Information on pricing, plans, and features.\n- GeneralAssistant: General-purpose queries and tasks.\n\nPlease select the most appropriate agent for your needs and clearly state your request (including a self-introduction).", "example": "```text\n// Immediate Communication Example\nI am the main AI, and the user needs a summary of recent developments in AI ethics.\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」AgentAssistant「末」,\nagent_name:「始」SupportAgent「末」,\nprompt:「始」Hello SupportAgent, I am the main AI. The user wants to understand the key advancements and challenges in AI ethics over the last six months. Please prepare a detailed report.「末」\n<<<[END_TOOL_REQUEST]>>>\n\n// Scheduled Communication Example\nI am the project manager. I need to remind the team about the deadline tomorrow.\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」AgentAssistant「末」,\nagent_name:「始」GeneralAssistant「末」,\nprompt:「始」Hello GeneralAssistant, this is the project manager. Please remember to send a team-wide announcement tomorrow morning regarding the project submission deadline at 5 PM.「末」,\ntimely_contact:「始」2025-10-26-09:00「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}