# CrossRef API settings for CrossRefDailyPapers plugin

# The bibliographic query for CrossRef API.
# Example: "Long-read Sequencing" OR metagenome OR "microbial genomics" OR "pathogen detection"
CROSSREF_QUERY_BIBLIOGRAPHIC='"Long-read Sequencing" OR metagenome OR "microbial genomics" OR "pathogen detection"'

# Number of results to fetch from CrossRef API.
CROSSREF_ROWS=300

# Number of days to fetch papers from, including today (e.g., 1 for today, 2 for today and yesterday).
# Default is 1 if not specified.
CROSSREF_DAYS_RANGE=2

# Enable debug mode for more verbose logging. Set to true or false.
# If false, some non-critical errors during data fetching/processing will be suppressed from the logs.
CROSSREF_DEBUG_MODE=false 