# WeatherReporter 插件 (天气预报员)

这是一个 `static` 类型的插件，其主要目的是定期获取指定城市的详细天气信息，并将这些信息提供给系统，以便在处理用户请求时通过系统提示词中的占位符注入。

## 功能

- 使用 **和风天气 API** 获取丰富的天气数据，包括：
  - 实时空气质量 (AQI, PM2.5 等)
  - 天气预警
  - 未来逐小时天气预报
  - 未来多日天气预报 (最多15天)
  - 月相和月升月落时间
  - 太阳高度角和方位角
- 将获取到的天气信息通过 `{{VCPWeatherInfo}}` 占位符在系统提示词中提供。
- 智能缓存机制，包括城市信息、格式化文本和原始JSON数据，以提高性能和API请求失败时的容错能力。

## 配置

插件的配置分为两部分：

### 1. 全局配置

请在主项目的 `config.env` 文件中配置以下**必需**变量：

- `VarCity`: 需要获取天气的城市名称 (例如: `北京`).
- `WeatherKey`: 您的和风天气 API Key.
- `WeatherUrl`: 和风天气 API 的请求域名 (例如: `devapi.qweather.com`).

### 2. 插件特定配置 (可选)

在 `Plugin/WeatherReporter/config.env` 文件中，您可以自定义以下选项：

- `forecastDays`: 设置获取未来天气预报的天数。
  - **支持范围**: `3`, `7`, `10`, `15`。插件会根据设定的天数自动选择最合适的API端点。
  - **默认值**: `7`

- `hourlyForecastInterval`: 设置24小时天气预报的**显示间隔**。
  - **说明**: 控制在最终输出中，每隔多少小时显示一条预报。
  - **默认值**: `2` (小时)

- `hourlyForecastCount`: 设置24小时天气预报总共**显示**的条目数。
  - **说明**: 与 `hourlyForecastInterval` 结合使用，决定了预报的时间跨度和密度。
  - **默认值**: `12`

**示例**: `hourlyForecastInterval=2`, `hourlyForecastCount=12` 将会获取并展示未来24小时内、每2小时一次的天气数据。

## 更新频率

根据 `plugin-manifest.json` 中的 `refreshIntervalCron` 设置，插件默认为每 8 小时自动更新一次天气信息。

## 缓存机制

插件使用三种不同的缓存文件来优化性能和数据可靠性：

- `city_cache.txt`: 缓存城市的地理位置信息 (ID, 经纬度, 时区等)。当城市名称相同时，避免重复请求地理位置API。
- `weather_cache.txt`: 缓存最终格式化好的、用于注入到 `{{VCPWeatherInfo}}` 的**纯文本天气报告**。当API请求失败时，会使用此缓存作为备用数据。
- `weather_cache.json`: 缓存从和风天气API获取的**原始JSON数据**。这主要用于调试，并为未来可能的功能扩展提供数据源。
