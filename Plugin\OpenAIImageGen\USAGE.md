# OpenAI 兼容图像生成插件使用指南

## 🚀 快速开始

### 1. 配置您的 API

编辑项目根目录的 `config.env` 文件，找到 OpenAI 插件配置部分：

```env
# OpenAI API 密钥 - 请替换为您的实际API密钥
OPENAI_API_KEY=your_openai_api_key_here

# OpenAI API 基础URL - 请替换为您的实际API地址
OPENAI_API_URL=https://api.openai.com
```

**重要提示**：
- 将 `your_openai_api_key_here` 替换为您的实际 API 密钥
- 将 `https://api.openai.com` 替换为您的 API 服务地址
- 确保您的 API 服务支持 `/v1/images/generations` 端点

### 2. 启动 VCP 服务

确保 VCP 服务正在运行，插件会自动加载。

### 3. 开始使用

在与 AI 的对话中，使用以下格式请求图像生成：

## 📝 使用示例

### 基础用法

```
请帮我生成一张图片：一只可爱的橘猫坐在樱花树下

<<<[TOOL_REQUEST]>>>
tool_name:「始」OpenAIImageGen「末」,
prompt:「始」一只可爱的橘猫坐在樱花树下，春天的阳光透过花瓣洒在猫咪身上，温馨治愈的画面「末」
<<<[END_TOOL_REQUEST]>>>
```

### 高级用法

```
生成一张高质量的宽屏壁纸：

<<<[TOOL_REQUEST]>>>
tool_name:「始」OpenAIImageGen「末」,
prompt:「始」未来科技城市的夜景，霓虹灯闪烁，赛博朋克风格，高楼大厦林立，飞行汽车穿梭其间「末」,
size:「始」1792x1024「末」,
quality:「始」hd「末」,
style:「始」vivid「末」
<<<[END_TOOL_REQUEST]>>>
```

### 批量生成

```
生成多张不同风格的图片：

<<<[TOOL_REQUEST]>>>
tool_name:「始」OpenAIImageGen「末」,
prompt:「始」一朵盛开的玫瑰花，细节丰富，光影效果优美「末」,
n:「始」3「末」,
quality:「始」hd「末」
<<<[END_TOOL_REQUEST]>>>
```

## 🎛️ 参数详解

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `prompt` | string | ✅ | - | 图像生成提示词，支持中英文 |
| `size` | string | ❌ | 1024x1024 | 图像尺寸 |
| `quality` | string | ❌ | standard | 图像质量 |
| `style` | string | ❌ | vivid | 图像风格 |
| `n` | integer | ❌ | 1 | 生成数量 |

### 尺寸选项 (size)
- `1024x1024` - 正方形，适合头像、图标
- `1792x1024` - 宽屏，适合横向壁纸、横幅
- `1024x1792` - 竖屏，适合手机壁纸、海报

### 质量选项 (quality)
- `standard` - 标准质量，生成速度快
- `hd` - 高清质量，细节更丰富，生成时间较长

### 风格选项 (style)
- `vivid` - 鲜艳风格，色彩饱和度高，对比强烈
- `natural` - 自然风格，色彩柔和，更接近真实

### 数量选项 (n)
- 范围：1-4
- 建议：单次生成不超过 2 张以节省资源

## 🎨 提示词技巧

### 中文提示词
```
一只穿着小裙子的柴犬在公园里奔跑，阳光明媚，绿草如茵，背景虚化
```

### 英文提示词
```
A cute Shiba Inu wearing a small dress running in a park, bright sunlight, green grass, blurred background
```

### 风格描述
```
水彩画风格的山水画，中国传统绘画技法，淡雅色彩，意境深远
```

### 技术参数
```
高分辨率，8K画质，专业摄影，景深效果，柔和光线，电影级画质
```

## 🔧 故障排除

### 常见错误及解决方案

#### 1. API 密钥错误
**错误信息**：`API request failed with status 401`
**解决方案**：
- 检查 `OPENAI_API_KEY` 是否正确
- 确认 API 密钥有图像生成权限
- 检查 API 密钥是否过期

#### 2. API 地址错误
**错误信息**：`Failed to fetch` 或连接超时
**解决方案**：
- 检查 `OPENAI_API_URL` 格式是否正确
- 确认网络连接正常
- 测试 API 服务是否可访问

#### 3. 参数错误
**错误信息**：`Invalid parameter` 相关错误
**解决方案**：
- 检查图像尺寸是否为支持的值
- 确认生成数量在 1-4 范围内
- 检查提示词是否过长

#### 4. 本地保存失败
**错误信息**：保存相关错误
**解决方案**：
- 检查 `LOCAL_SAVE_PATH` 目录权限
- 确保有足够的磁盘空间
- 检查文件路径是否有效

### 调试模式

如需查看详细日志，在 `config.env` 中设置：
```env
DebugMode=true
```

## 🌟 最佳实践

### 1. 提示词优化
- **具体描述**：避免模糊的词汇，使用具体的描述
- **风格指定**：明确指定想要的艺术风格
- **细节丰富**：包含光线、色彩、构图等细节

### 2. 参数选择
- **尺寸选择**：根据用途选择合适的尺寸
- **质量平衡**：标准质量通常已足够，高清质量用于重要场合
- **批量生成**：一次生成多张可以获得更多选择

### 3. 资源管理
- **合理使用**：避免频繁生成大量图片
- **本地保存**：启用本地保存以便后续使用
- **定期清理**：定期清理不需要的生成图片

## 📞 技术支持

如果遇到问题，请：
1. 检查配置文件是否正确
2. 查看调试日志
3. 确认 API 服务状态
4. 参考故障排除指南

插件版本：v1.0.0
更新日期：2025-01-27
