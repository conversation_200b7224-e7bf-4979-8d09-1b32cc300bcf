{"manifestVersion": "1.0.0", "name": "FRPSInfoProvider", "version": "1.0.0", "displayName": "FRPS 设备信息提供器", "description": "定期从FRPS服务器获取所有类型的代理设备信息，并整合成一个文本文件供占位符使用。", "author": "B3000Kcn", "pluginType": "static", "entryPoint": {"command": "node FRPSInfoProvider.js"}, "communication": {"protocol": "stdio"}, "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{FRPSAllProxyInfo}}", "description": "所有类型FRPS代理的实时设备信息汇总文本 (TCP, UDP, HTTP, HTTPS, TCPMUX, STCP, SUDP)。"}], "invocationCommands": []}, "refreshIntervalCron": "*/10 * * * * *", "configSchema": {"FRPSBaseUrl": {"type": "string", "description": "您的FRPS服务器基础URL (例如: https://frps.example.com)，不包含 /api 部分。", "default": "", "required": true}, "FRPSAdminUser": {"type": "string", "description": "您的FRPS API管理员用户名。", "default": "admin", "required": true}, "FRPSAdminPassword": {"type": "string", "description": "您的FRPS API管理员密码。", "default": "", "required": true}, "DebugMode": {"type": "boolean", "description": "是否为此插件启用详细的调试日志输出到stderr。", "default": false, "required": false}}}