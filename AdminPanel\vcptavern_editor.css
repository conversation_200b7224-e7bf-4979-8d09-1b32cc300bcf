body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--primary-bg);
    color: var(--primary-text);
    margin: 0;
    padding: 20px;
    display: block; /* Override display:flex from style.css */
}

.container {
    display: block; /* Override display:flex from style.css */
    max-width: 1200px;
    margin: auto;
    background: var(--secondary-bg);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid var(--border-color);
}

h1, h2 {
    color: var(--highlight-text);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.preset-manager {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.preset-manager select, .preset-manager button {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 14px;
}

.preset-manager button {
    cursor: pointer;
    background-color: var(--button-bg);
    color: white; /* Button text is usually white on colored background */
    border-color: var(--button-bg);
    transition: background-color 0.2s;
}
html[data-theme='light'] .preset-manager button {
    color: var(--primary-text-light);
}


.preset-manager button:hover {
    background-color: var(--button-hover-bg);
}

.preset-manager button#delete-preset {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}
.preset-manager button#delete-preset:hover {
    background-color: var(--danger-hover-bg);
}

#editor-container.hidden {
    display: none;
}

.preset-meta input, .preset-meta textarea {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    box-sizing: border-box;
}

#rules-list {
    margin-top: 20px;
}

.rule-card {
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
}

.rule-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--primary-text);
}

.rule-controls button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    margin-left: 10px;
}

.delete-rule { color: var(--danger-color); }
.toggle-rule { color: var(--highlight-text); }

.rule-body {
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.rule-body .form-group {
    display: flex;
    flex-direction: column;
}

.rule-body label {
    font-size: 12px;
    color: var(--secondary-text);
    margin-bottom: 5px;
}

.rule-body input, .rule-body select, .rule-body textarea {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    box-sizing: border-box;
}

.rule-body textarea {
    min-height: 100px;
    resize: vertical;
}

#add-rule, #save-preset {
    padding: 10px 15px;
    border-radius: 4px;
    border: none;
    background-color: #28a745; /* Keeping green for save/add */
    color: white;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
    margin-right: 10px;
}

#add-rule:hover, #save-preset:hover {
    background-color: #218838;
}

.dragging {
    opacity: 0.5;
    background: var(--accent-bg);
}