#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

console.log('🎨 OpenAI 兼容图像生成插件安装向导\n');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function main() {
    try {
        console.log('欢迎使用 OpenAI 兼容图像生成插件！');
        console.log('此向导将帮助您配置插件。\n');

        // 检查主配置文件
        const mainConfigPath = path.resolve('../../config.env');
        if (!fs.existsSync(mainConfigPath)) {
            console.log('❌ 未找到主配置文件 config.env');
            console.log('请确保在 VCP 项目根目录下运行此脚本。');
            process.exit(1);
        }

        // 获取用户输入
        const apiKey = await question('请输入您的 OpenAI API 密钥: ');
        if (!apiKey.trim()) {
            console.log('❌ API 密钥不能为空');
            process.exit(1);
        }

        const apiUrl = await question('请输入您的 API 基础URL (默认: https://api.openai.com): ') || 'https://api.openai.com';
        
        const model = await question('请输入图像模型名称 (默认: dall-e-3): ') || 'dall-e-3';
        
        const saveLocally = await question('是否保存图片到本地? (y/n, 默认: y): ') || 'y';
        const saveLocallyBool = saveLocally.toLowerCase().startsWith('y');
        
        let savePath = 'image/openai_generated';
        if (saveLocallyBool) {
            savePath = await question('本地保存路径 (默认: image/openai_generated): ') || 'image/openai_generated';
        }

        // 读取主配置文件
        let configContent = fs.readFileSync(mainConfigPath, 'utf8');

        // 检查是否已有 OpenAI 配置
        if (configContent.includes('OPENAI_API_KEY=')) {
            const update = await question('检测到已有 OpenAI 配置，是否更新? (y/n): ');
            if (!update.toLowerCase().startsWith('y')) {
                console.log('取消安装。');
                process.exit(0);
            }
            
            // 更新现有配置
            configContent = configContent.replace(/OPENAI_API_KEY=.*/g, `OPENAI_API_KEY=${apiKey}`);
            configContent = configContent.replace(/OPENAI_API_URL=.*/g, `OPENAI_API_URL=${apiUrl}`);
            configContent = configContent.replace(/OPENAI_IMAGE_MODEL=.*/g, `OPENAI_IMAGE_MODEL=${model}`);
            configContent = configContent.replace(/SAVE_IMAGES_LOCALLY=.*/g, `SAVE_IMAGES_LOCALLY=${saveLocallyBool}`);
            configContent = configContent.replace(/LOCAL_SAVE_PATH=.*/g, `LOCAL_SAVE_PATH=${savePath}`);
        } else {
            // 添加新配置
            const newConfig = `
# -------------------------------------------------------------------
# [OpenAI 兼容图像生成插件配置]
# -------------------------------------------------------------------
OPENAI_API_KEY=${apiKey}
OPENAI_API_URL=${apiUrl}
OPENAI_IMAGE_MODEL=${model}
DEFAULT_IMAGE_SIZE=1024x1024
DEFAULT_IMAGE_QUALITY=standard
DEFAULT_IMAGE_STYLE=vivid
MAX_RETRIES=3
SAVE_IMAGES_LOCALLY=${saveLocallyBool}
LOCAL_SAVE_PATH=${savePath}
`;
            configContent += newConfig;
        }

        // 写入配置文件
        fs.writeFileSync(mainConfigPath, configContent);

        // 创建保存目录
        if (saveLocallyBool) {
            const fullSavePath = path.resolve('../../', savePath);
            if (!fs.existsSync(fullSavePath)) {
                fs.mkdirSync(fullSavePath, { recursive: true });
                console.log(`✅ 创建图片保存目录: ${fullSavePath}`);
            }
        }

        // 检查工具列表配置
        const toolListPath = path.resolve('../../TVStxt/supertool.txt');
        if (fs.existsSync(toolListPath)) {
            const toolContent = fs.readFileSync(toolListPath, 'utf8');
            if (!toolContent.includes('OpenAIImageGen')) {
                console.log('\n⚠️  注意：请手动将 OpenAIImageGen 添加到工具列表中。');
                console.log('在 TVStxt/supertool.txt 文件的多媒体生成类部分添加插件说明。');
            } else {
                console.log('✅ 工具列表已包含 OpenAIImageGen');
            }
        }

        console.log('\n🎉 安装完成！');
        console.log('\n📋 配置摘要:');
        console.log(`- API URL: ${apiUrl}`);
        console.log(`- 模型: ${model}`);
        console.log(`- 本地保存: ${saveLocallyBool ? '是' : '否'}`);
        if (saveLocallyBool) {
            console.log(`- 保存路径: ${savePath}`);
        }

        console.log('\n🚀 使用方法:');
        console.log('在与 AI 的对话中使用以下格式:');
        console.log('');
        console.log('<<<[TOOL_REQUEST]>>>');
        console.log('tool_name:「始」OpenAIImageGen「末」,');
        console.log('prompt:「始」您的图片描述「末」');
        console.log('<<<[END_TOOL_REQUEST]>>>');
        console.log('');
        console.log('详细使用说明请查看 USAGE.md 文件。');

    } catch (error) {
        console.error('❌ 安装过程中出现错误:', error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

main();
