{"name": "FileTreeGenerator", "displayName": "文件树生成器", "version": "1.0.0", "description": "扫描指定目录的文件夹结构，并通过占位符提供给AI。", "pluginType": "static", "communication": {"protocol": "stdio", "timeout": 60000}, "entryPoint": {"command": "node FileTreeGenerator.js"}, "refreshIntervalCron": "*/5 * * * *", "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{VCPFilestructureInfo}}", "description": "获取由config.env中TARGET_DIRECTORY指定目录的文件夹结构树。", "isDynamic": true}]}, "configSchema": [{"key": "TARGET_DIRECTORY", "type": "string", "description": "需要扫描的目标文件夹的绝对路径。", "defaultValue": ""}, {"key": "EXCLUDE_DIRS", "type": "string", "description": "扫描时需要排除的文件夹名称列表，用逗号分隔。例如：.git,node_modules", "defaultValue": ""}]}