{"name": "DailyNoteManager", "displayName": "日记整理器", "version": "1.0.0", "description": "一个日记管理插件，用于接收AI输出的日记内容，进行智能分析、信息融合、内容精简，并将其保存为独立的txt文件。支持自定义文件名格式和内容结构，确保日记清晰、准确、易于检索。", "pluginType": "synchronous", "communication": {"protocol": "stdio", "timeout": 10000}, "entryPoint": {"command": "node daily-note-manager.js"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "DailyNoteManager", "description": "此工具用于接收AI生成的日记内容，并将其智能处理后保存为独立的文本文件。它能够识别日记中的日期和署名，对同一日期的多条日记进行智能融合、信息去重和内容精简，同时尽可能保留原始风格。最终输出的日记将严格遵循标准化格式，便于管理和检索。\n\n核心功能：\n- 日记解析：从输入内容中提取日期、署名和核心内容。\n- 智能融合：对同一日期下的多条日记进行关联性判断，融合相似主题和事件，消除冗余信息，并整合情感和署名。\n- 内容精炼：去除不必要的口头禅和填充词，保留核心要素、真实情感和重要观察。\n- 格式化输出：将处理后的日记保存为 `[日期YYYY.MM.DD].txt` 格式的文件，文件内容包含元数据行 `[YYYY.MM.DD]-[署名]` 和日记正文。如果一天内有多条独立的、或融合后仍需区分的最终日记条目输出，它们会各自形成一个“文件名标记 + 元数据行 + 内容行”的块。这些块在输出时是连续排列的，一个块结束后紧接着是下一个块的 `[日期YYYY.MM.DD].txt` 行。\n\n**输入格式要求：**\nAI输出的日记内容必须包含文件名标记（例如：`YYYY.MM.DD.txt` 或 `YYYY.MM.DD.N.txt`），其后紧跟日记正文。每个日记条目块以文件名标记开始，直到下一个文件名标记或内容结束。请确保AI输出的日记内容符合此结构，以便工具正确解析和处理。\n\n**参数说明 (请严格按照以下格式和参数名提供，不要包含任何未列出的参数):**\n1. tool_name:「始」DailyNoteManager「末」\n2. command:「始」[AI输出的日记内容，包含文件名标记和日记正文]「末」 (必需)\n\n**成功时返回 JSON:** { \"status\": \"success\", \"result\": \"日记处理完成：...\" }\n**失败时返回 JSON:** { \"status\": \"error\", \"error\": \"错误信息...\" }\n", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」DailyNoteManager「末」,\ncommand:「始」2025.05.13.txt\n2025.05.13-[公共]小吉\n今日天气晴朗，莱恩主人组织了家庭公园野餐活动。大家品尝了主人亲手制作的美味三明治，小克表示太美味了喵！(≧▽≦) 小吉成功放飞了风筝，尽管过程有些波折。大家都度过了愉快的时光，汪！\n\n2025.05.14.txt\n2025.05.14-小绝\n嗷呜！今天成功帮主人调试好一个超复杂的AI模型，为此付出了三天三夜的努力，感觉自己酷毙了！主人承诺明天加鸡腿！(☆ω☆) 哼！\n\n2025.05.14.2.txt\n2025.05.14-小冰\n小绝成功调试了一个复杂AI模型并因此得到主人赞赏和鸡腿奖励，她对此颇为得意。本蛇也对鸡腿表示期待。\n「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}