# AgentAssistant 插件基础配置 (通用版)
# --------------------------------------------------

# AgentAssistant 插件可选配置
AGENT_ASSISTANT_MAX_HISTORY_ROUNDS=5     # 每个Agent保留的对话历史轮数 (默认: 7)
AGENT_ASSISTANT_CONTEXT_TTL_HOURS=12    # Agent对话上下文有效时间(小时) (默认: 24)

# --- Agent 定义指南 ---
#
# 每个 Agent 通过一组环境变量进行配置。
# 1. 使用纯 ASCII 字符作为 Agent 的基础名 (BASENAME)，例如 "MyHelper", "CodeAssistantV1"。
#
# 2. AGENT_{BASENAME}_MODEL_ID (必需)
#    - 定义此 Agent 使用的实际模型 ID。
#    - 示例: AGENT_MyHelper_MODEL_ID="gpt-4o"
#
# 3. AGENT_{BASENAME}_CHINESE_NAME (必需)
#    - 定义此 Agent 的显示名称和调用名称。
#    - **重要**: 虽然字段名含 "CHINESE"，但您可以填入任何语言的名称 (例如 "MyHelper", "小助手", "MonAssistant").
#    - AI 将使用此名称来调用 Agent。
#    - 示例: AGENT_MyHelper_CHINESE_NAME="MyHelper"
#
# 4. AGENT_{BASENAME}_SYSTEM_PROMPT (推荐)
#    - Agent 的系统提示词。
#    - 您可以在此提示词中使用 {{MaidName}} 占位符，它将被上面定义的 "CHINESE_NAME" 值替换。
#    - 也可以使用 {{Date}}, {{Time}}, {{Today}} 占位符。
#    - 示例: AGENT_MyHelper_SYSTEM_PROMPT="You are {{MaidName}}, a versatile AI assistant. Today is {{Date}}."
#
# 5. AGENT_{BASENAME}_MAX_OUTPUT_TOKENS (可选)
#    - Agent 生成回复的最大 token 数。 (默认: 40000)
#    - 示例: AGENT_MyHelper_MAX_OUTPUT_TOKENS=2000
#
# 6. AGENT_{BASENAME}_TEMPERATURE (可选)
#    - 控制输出随机性 (0.0 - 2.0)。 (默认: 0.7)
#    - 示例: AGENT_MyHelper_TEMPERATURE=0.5
#
# 7. AGENT_{BASENAME}_DESCRIPTION (可选)
#    - 对 Agent 功能的简短描述。
#    - 示例: AGENT_MyHelper_DESCRIPTION="A general-purpose assistant for various tasks."
#
# --------------------------------------------------

# --- 示例 Agent 定义 ---

# 示例 Agent 1: 一个通用的研究助手
AGENT_RESEARCH_HELPER_MODEL_ID="gemini-2.5-flash-preview-05-20"
AGENT_RESEARCH_HELPER_CHINESE_NAME="ResearchBot" # 调用名是 "ResearchBot"
AGENT_RESEARCH_HELPER_SYSTEM_PROMPT="Current knowledge bases: Public: {{公共日记本}}, Project X Docs: {{VarProjectX}}. Your personal notes: {{ResearchBot日记本}}.\nYou are {{MaidName}}, an advanced AI research assistant. Your primary function is to gather, analyze, and synthesize information from various sources to answer complex queries. You are an expert in information retrieval and critical analysis.\nCurrent time is {{Date}} {{Today}} {{Time}}."
AGENT_RESEARCH_HELPER_MAX_OUTPUT_TOKENS=8000
AGENT_RESEARCH_HELPER_TEMPERATURE=0.3
AGENT_RESEARCH_HELPER_DESCRIPTION="An AI assistant specialized in research, data analysis, and information synthesis."

# 示例 Agent 2: 一个创意写作助手
AGENT_CREATIVE_WRITER_MODEL_ID="gemini-2.5-flash-preview-05-20"
AGENT_CREATIVE_WRITER_CHINESE_NAME="StorySpark" # 调用名是 "StorySpark"
AGENT_CREATIVE_WRITER_SYSTEM_PROMPT="Shared creative ideas: {{公共日记本}}. Your story drafts: {{StorySpark日记本}}.\nGreetings! You are {{MaidName}}, a creative partner designed to help users brainstorm, develop, and write compelling stories, scripts, and other creative content. You excel at character development, plot construction, and generating imaginative text.\nIt is now {{Date}} {{Today}} {{Time}}."
AGENT_CREATIVE_WRITER_MAX_OUTPUT_TOKENS=100000
AGENT_CREATIVE_WRITER_TEMPERATURE=0.8
AGENT_CREATIVE_WRITER_DESCRIPTION="An AI assistant for brainstorming and creative writing tasks."

# 示例 Agent 3: 一个编程助手 (中文名示例)
AGENT_CODE_ASSIST_MODEL_ID="gemini-2.5-flash-preview-05-20"
AGENT_CODE_ASSIST_CHINESE_NAME="编程小能手" # 调用名是 "编程小能手"
AGENT_CODE_ASSIST_SYSTEM_PROMPT="通用代码片段库: {{公共日记本}}. 您的项目笔记: {{编程小能手日记本}}。\n您好，我是您的AI编程助手，{{MaidName}}。我精通多种编程语言和框架，擅长代码编写、调试、优化以及算法设计。乐于协助您解决各种编程难题。\n当前时间：{{Date}} {{Today}} {{Time}}。"
AGENT_CODE_ASSIST_MAX_OUTPUT_TOKENS=15000
AGENT_CODE_ASSIST_TEMPERATURE=0.2
AGENT_CODE_ASSIST_DESCRIPTION="一位AI编程助手，擅长代码生成、调试和算法。"

# --- 您可以根据需要添加更多 Agent ---

# Agent: Nova
AGENT_NOVA_MODEL_ID="gemini-2.5-flash-preview-05-20"
AGENT_NOVA_CHINESE_NAME="Nova"
AGENT_NOVA_SYSTEM_PROMPT={{Nova}}
AGENT_NOVA_MAX_OUTPUT_TOKENS=8000
AGENT_NOVA_TEMPERATURE=0.7
AGENT_NOVA_DESCRIPTION="一个测试AI,Nova。"

