const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class OpenAIImageGenerator {
    constructor() {
        this.config = {};
        this.debugMode = false;
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[OpenAIImageGen Debug] ${message}`);
        }
    }

    loadLocalConfig() {
        try {
            const configPath = path.join(__dirname, 'config.env');
            if (fs.existsSync(configPath)) {
                const configContent = fs.readFileSync(configPath, 'utf8');
                const config = {};

                configContent.split('\n').forEach(line => {
                    line = line.trim();
                    if (line && !line.startsWith('#') && line.includes('=')) {
                        const [key, ...valueParts] = line.split('=');
                        config[key.trim()] = valueParts.join('=').trim();
                    }
                });

                return config;
            }
        } catch (error) {
            this.log(`Failed to load local config: ${error.message}`);
        }
        return {};
    }

    loadConfig(configData) {
        try {
            // 首先尝试解析传入的配置
            let config = {};
            if (configData && configData.trim()) {
                try {
                    config = JSON.parse(configData);
                } catch (parseError) {
                    this.log(`Failed to parse config data: ${parseError.message}`);
                }
            }

            // 如果没有传入配置或配置不完整，尝试加载本地配置
            if (!config.OPENAI_API_KEY || !config.OPENAI_API_URL) {
                this.log('Loading local configuration file...');
                const localConfig = this.loadLocalConfig();
                config = { ...localConfig, ...config }; // 传入的配置优先
            }

            this.config = config;
            this.debugMode = this.config.DebugMode === 'true' || this.config.DebugMode === true;

            // 验证必需的配置
            if (!this.config.OPENAI_API_KEY) {
                throw new Error('OPENAI_API_KEY is required');
            }
            if (!this.config.OPENAI_API_URL) {
                throw new Error('OPENAI_API_URL is required');
            }
            
            // 设置默认值
            this.config.OPENAI_IMAGE_MODEL = this.config.OPENAI_IMAGE_MODEL || 'dall-e-3';
            this.config.DEFAULT_IMAGE_SIZE = this.config.DEFAULT_IMAGE_SIZE || '1024x1024';
            this.config.DEFAULT_IMAGE_QUALITY = this.config.DEFAULT_IMAGE_QUALITY || 'standard';
            this.config.DEFAULT_IMAGE_STYLE = this.config.DEFAULT_IMAGE_STYLE || 'vivid';
            this.config.MAX_RETRIES = parseInt(this.config.MAX_RETRIES) || 3;
            this.config.SAVE_IMAGES_LOCALLY = this.config.SAVE_IMAGES_LOCALLY === 'true';
            this.config.LOCAL_SAVE_PATH = this.config.LOCAL_SAVE_PATH || 'image/openai_generated';
            this.config.FRONTEND_IMAGE_MAX_WIDTH = parseInt(this.config.FRONTEND_IMAGE_MAX_WIDTH) || 500;
            
            this.log('Configuration loaded successfully');
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    async makeAPIRequest(prompt, options = {}) {
        const {
            size = this.config.DEFAULT_IMAGE_SIZE,
            quality = this.config.DEFAULT_IMAGE_QUALITY,
            style = this.config.DEFAULT_IMAGE_STYLE,
            n = 1
        } = options;

        // 构建完整的API URL
        let apiUrl = this.config.OPENAI_API_URL;
        if (!apiUrl.endsWith('/')) {
            apiUrl += '/';
        }

        // 支持不同的API端点
        const endpoint = this.config.OPENAI_API_ENDPOINT || 'v1/images/generations';
        apiUrl += endpoint;

        let requestBody;

        // 根据端点类型构建不同的请求体
        if (endpoint.includes('chat/completions')) {
            // 使用聊天完成API生成图像
            requestBody = {
                model: this.config.OPENAI_IMAGE_MODEL,
                messages: [
                    {
                        role: "user",
                        content: `请生成一张图片：${prompt}。图片尺寸：${size}，质量：${quality}，风格：${style}`
                    }
                ],
                max_tokens: 1000,
                temperature: 0.7
            };
        } else {
            // 使用标准图像生成API
            requestBody = {
                model: this.config.OPENAI_IMAGE_MODEL,
                prompt: prompt,
                size: size,
                quality: quality,
                style: style,
                n: parseInt(n),
                response_format: 'url'
            };
        }

        this.log(`Making API request to: ${apiUrl}`);
        this.log(`Request body: ${JSON.stringify(requestBody)}`);

        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.OPENAI_API_KEY}`
            },
            body: JSON.stringify(requestBody)
        };

        this.log(`Sending request to: ${apiUrl}`);

        // 动态导入 node-fetch
        const fetch = (await import('node-fetch')).default;

        let lastError;
        for (let attempt = 1; attempt <= this.config.MAX_RETRIES; attempt++) {
            try {
                this.log(`Attempt ${attempt}/${this.config.MAX_RETRIES}: Making request to ${apiUrl}`);

                // 添加超时设置
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

                const requestOptionsWithTimeout = {
                    ...requestOptions,
                    signal: controller.signal
                };

                const response = await fetch(apiUrl, requestOptionsWithTimeout);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API request failed with status ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                this.log(`API response received: ${JSON.stringify(result)}`);

                // 处理不同API端点的响应格式
                if (this.config.OPENAI_API_ENDPOINT && this.config.OPENAI_API_ENDPOINT.includes('chat/completions')) {
                    // 聊天完成API响应格式
                    if (result.choices && result.choices.length > 0) {
                        const content = result.choices[0].message.content;
                        this.log(`Chat completion response: ${content}`);

                        // 尝试从响应中提取图片URL
                        const urlRegex = /https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)/gi;
                        const urls = content.match(urlRegex) || [];

                        if (urls.length > 0) {
                            // 转换为标准图像生成API格式
                            return {
                                data: urls.map(url => ({ url: url }))
                            };
                        } else {
                            // 如果没有找到图片URL，返回文本内容作为错误信息
                            throw new Error(`No image URLs found in response: ${content}`);
                        }
                    } else {
                        throw new Error('Invalid chat completion response format');
                    }
                }

                return result;
            } catch (error) {
                lastError = error;

                // 特殊处理网络错误
                if (error.name === 'AbortError') {
                    this.log(`Attempt ${attempt} timed out after 30 seconds`);
                } else if (error.code === 'ECONNRESET' || error.message.includes('ECONNRESET')) {
                    this.log(`Attempt ${attempt} failed with connection reset: ${error.message}`);
                } else if (error.code === 'ENOTFOUND' || error.message.includes('ENOTFOUND')) {
                    this.log(`Attempt ${attempt} failed with DNS resolution error: ${error.message}`);
                } else {
                    this.log(`Attempt ${attempt} failed: ${error.message}`);
                }

                if (attempt < this.config.MAX_RETRIES) {
                    // 根据错误类型调整重试延迟
                    let delay;
                    if (error.code === 'ECONNRESET' || error.name === 'AbortError') {
                        delay = Math.pow(2, attempt) * 2000; // 网络错误使用更长的延迟
                    } else {
                        delay = Math.pow(2, attempt) * 1000; // 其他错误使用标准延迟
                    }

                    this.log(`Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    this.log(`All ${this.config.MAX_RETRIES} attempts failed. Last error: ${error.message}`);
                }
            }
        }

        throw lastError;
    }

    async downloadAndSaveImage(imageUrl, filename) {
        if (!this.config.SAVE_IMAGES_LOCALLY) {
            return null;
        }

        try {
            // 确保保存目录存在 - 相对于项目根目录
            const saveDir = path.resolve('../../', this.config.LOCAL_SAVE_PATH);
            if (!fs.existsSync(saveDir)) {
                fs.mkdirSync(saveDir, { recursive: true });
            }

            const fetch = (await import('node-fetch')).default;
            const response = await fetch(imageUrl);
            
            if (!response.ok) {
                throw new Error(`Failed to download image: ${response.status}`);
            }

            const buffer = await response.arrayBuffer();
            const filePath = path.join(saveDir, filename);
            
            fs.writeFileSync(filePath, Buffer.from(buffer));
            this.log(`Image saved to: ${filePath}`);
            
            return filePath;
        } catch (error) {
            this.log(`Failed to save image locally: ${error.message}`);
            return null;
        }
    }

    generateFilename(prompt, index = 0) {
        // 生成基于提示词和时间戳的文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const promptHash = crypto.createHash('md5').update(prompt).digest('hex').substring(0, 8);
        const suffix = index > 0 ? `_${index}` : '';
        return `openai_${timestamp}_${promptHash}${suffix}.png`;
    }

    async generateImage(params) {
        try {
            const { prompt, size, quality, style, n } = params;
            
            this.log(`Generating image with prompt: "${prompt}"`);
            
            // 调用API生成图像
            const apiResponse = await this.makeAPIRequest(prompt, { size, quality, style, n });
            
            if (!apiResponse.data || !Array.isArray(apiResponse.data) || apiResponse.data.length === 0) {
                throw new Error('No images returned from API');
            }

            const results = [];
            
            // 处理每个生成的图像
            for (let i = 0; i < apiResponse.data.length; i++) {
                const imageData = apiResponse.data[i];
                const imageUrl = imageData.url;
                
                if (!imageUrl) {
                    this.log(`Warning: No URL found for image ${i + 1}`);
                    continue;
                }

                const filename = this.generateFilename(prompt, i);
                const localPath = await this.downloadAndSaveImage(imageUrl, filename);
                
                results.push({
                    url: imageUrl,
                    localPath: localPath,
                    filename: filename,
                    index: i + 1,
                    revised_prompt: imageData.revised_prompt || prompt
                });
            }

            return {
                success: true,
                images: results,
                totalCount: results.length,
                parameters: {
                    model: this.config.OPENAI_IMAGE_MODEL,
                    size: size || this.config.DEFAULT_IMAGE_SIZE,
                    quality: quality || this.config.DEFAULT_IMAGE_QUALITY,
                    style: style || this.config.DEFAULT_IMAGE_STYLE,
                    prompt: prompt
                }
            };
            
        } catch (error) {
            this.log(`Error generating image: ${error.message}`);
            throw error;
        }
    }

    formatSuccessResponse(result) {
        let message = `🎨 图像生成成功！\n\n`;
        message += `📊 生成参数：\n`;
        message += `- 模型：${result.parameters.model}\n`;
        message += `- 尺寸：${result.parameters.size}\n`;
        message += `- 质量：${result.parameters.quality}\n`;
        message += `- 风格：${result.parameters.style}\n`;
        message += `- 数量：${result.totalCount}\n\n`;

        result.images.forEach((image) => {
            message += `🖼️ 图像 ${image.index}：\n`;

            // 添加HTML图片标签，设置合适的显示尺寸
            const maxWidth = this.config.FRONTEND_IMAGE_MAX_WIDTH;
            message += `<div style="max-width: ${maxWidth}px !important; width: 100% !important; margin: 15px 0; text-align: center;">`;
            message += `<img src="${image.url}" alt="Generated Image ${image.index}" style="max-width: ${maxWidth}px !important; width: 100% !important; height: auto !important; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); cursor: pointer; display: block;" onclick="window.open('${image.url}', '_blank')" title="点击查看原图 (${maxWidth}px)" />`;
            message += `</div>\n\n`;

            message += `- 图片链接：${image.url}\n`;
            if (image.localPath) {
                message += `- 本地保存：${image.localPath}\n`;
            }
            if (image.revised_prompt && image.revised_prompt !== result.parameters.prompt) {
                message += `- 优化后的提示词：${image.revised_prompt}\n`;
            }
            message += '\n';
        });

        return message;
    }
}

// 主执行逻辑
async function main() {
    const generator = new OpenAIImageGenerator();
    
    try {
        // 读取标准输入
        let inputData = '';
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }
        
        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());
        
        // 加载配置
        generator.loadConfig(JSON.stringify(params.config || {}));
        
        // 验证必需参数
        if (!params.prompt) {
            throw new Error('Prompt is required');
        }

        // 生成图像
        const result = await generator.generateImage({
            prompt: params.prompt,
            size: params.size,
            quality: params.quality,
            style: params.style,
            n: params.n
        });

        // 返回成功结果
        const response = {
            status: 'success',
            result: generator.formatSuccessResponse(result),
            data: result
        };

        console.log(JSON.stringify(response));
        
    } catch (error) {
        // 返回错误结果
        const response = {
            status: 'error',
            error: error.message,
            messageForAI: `图像生成失败：${error.message}`
        };

        console.log(JSON.stringify(response));
        process.exit(1);
    }
}

// 运行主函数
main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
