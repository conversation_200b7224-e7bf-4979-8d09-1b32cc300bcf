// 简化的调试测试
async function main() {
    try {
        const input = await new Promise((resolve) => {
            let data = '';
            process.stdin.on('data', chunk => data += chunk);
            process.stdin.on('end', () => resolve(data.trim()));
        });

        console.error(`[DEBUG] Raw input: ${input}`);
        
        const request = JSON.parse(input);
        console.error(`[DEBUG] Parsed request: ${JSON.stringify(request)}`);
        
        const { command, ...params } = request;
        console.error(`[DEBUG] Command: ${command}`);
        console.error(`[DEBUG] Params: ${JSON.stringify(params)}`);

        let result;
        switch (command) {
            case 'list_plugins':
                console.error('[DEBUG] Executing list_plugins');
                result = { status: 'success', result: 'list_plugins executed', messageForAI: 'List plugins command executed' };
                break;
            case 'check_dependencies':
                console.error('[DEBUG] Executing check_dependencies');
                result = { status: 'success', result: 'check_dependencies executed', messageForAI: 'Check dependencies command executed' };
                break;
            default:
                console.error(`[DEBUG] Unknown command: ${command}`);
                result = { status: 'error', error: `Unknown command: ${command}` };
        }

        console.error(`[DEBUG] Final result: ${JSON.stringify(result)}`);
        console.log(JSON.stringify(result));
        process.exit(0);
    } catch (error) {
        console.error(`[DEBUG] Error: ${error.message}`);
        console.log(JSON.stringify({
            status: 'error',
            error: error.message
        }));
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
