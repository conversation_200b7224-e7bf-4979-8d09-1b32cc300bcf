{"manifestVersion": "1.0.0", "name": "OpenAIImageGen", "displayName": "OpenAI 兼容图像生成器", "version": "1.0.0", "description": "使用兼容 OpenAI API 的服务生成图像，支持 DALL-E 风格的图像生成。", "author": "VCP User", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node OpenAIImageGen.js"}, "communication": {"protocol": "stdio", "timeout": 60000}, "configSchema": {"OPENAI_API_KEY": "string", "OPENAI_API_URL": "string", "OPENAI_IMAGE_MODEL": "string", "DEFAULT_IMAGE_SIZE": "string", "DEFAULT_IMAGE_QUALITY": "string", "DEFAULT_IMAGE_STYLE": "string", "MAX_RETRIES": "integer", "SAVE_IMAGES_LOCALLY": "boolean", "LOCAL_SAVE_PATH": "string"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "GenerateImage", "description": "使用兼容OpenAI API的服务生成图像。请在您的回复中使用以下精确格式来请求图像生成，确保所有参数值都用「始」和「末」准确包裹：\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」OpenAIImageGen「末」,\nprompt:「始」(必需) 用于图像生成的详细提示词，支持中文和英文。「末」,\nsize:「始」(可选) 图像尺寸，可选值：\"1024x1024\"、\"1792x1024\"、\"1024x1792\"。默认：\"1024x1024\"。「末」,\nquality:「始」(可选) 图像质量，可选值：\"standard\"、\"hd\"。默认：\"standard\"。「末」,\nstyle:「始」(可选) 图像风格，可选值：\"vivid\"、\"natural\"。默认：\"vivid\"。「末」,\nn:「始」(可选) 生成图像数量，范围1-4。默认：1。「末」\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示：\n1. 当此工具执行完毕后，您将收到包含以下信息的结果：\n   - 生成图片的URL链接\n   - 图片的本地保存路径（如果启用了本地保存）\n   - 图片的详细信息（尺寸、质量等）\n2. 请在您的最终回复中，使用返回的图片URL为用户生成HTML的 `<img>` 标签来直接展示图片。\n3. 建议调整 `width` 属性为200-500像素以获得最佳显示效果。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」OpenAIImageGen「末」,\nprompt:「始」一只可爱的橘猫坐在樱花树下，春天的阳光透过花瓣洒在猫咪身上，温馨治愈的画面「末」,\nsize:「始」1024x1024「末」,\nquality:「始」hd「末」,\nstyle:「始」vivid「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}