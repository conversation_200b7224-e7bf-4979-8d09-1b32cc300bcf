{"manifestVersion": "1.0.0", "name": "SmartLearningManager", "displayName": "智能学习管理系统", "version": "1.0.0", "description": "AI驱动的个人学习管理系统，支持知识图谱构建、学习路径规划、进度跟踪、智能复习提醒、多媒体笔记管理等功能。基于艾宾浩斯遗忘曲线和认知科学原理优化学习效果。", "author": "VCP Education", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node SmartLearningManager.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"DebugMode": {"type": "boolean", "description": "是否启用详细的调试日志输出到stderr", "default": false, "required": false}}, "capabilities": {"invocationCommands": [{"command": "create_knowledge_map", "description": "创建和管理知识图谱节点。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」create_knowledge_map「末」,\ntopic:「始」(必需) 主题名称「末」,\ncontent:「始」(必需) 学习内容或笔记「末」,\ntags:「始」(可选) 标签，用逗号分隔「末」,\ndifficulty:「始」(可选) 难度级别：beginner、intermediate、advanced。默认：intermediate「末」,\nrelated_topics:「始」(可选) 相关主题，用逗号分隔「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：创建知识图谱节点，建立知识点之间的关联关系，支持标签分类和难度分级。"}, {"command": "plan_learning_path", "description": "制定个性化学习路径规划。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」plan_learning_path「末」,\ngoal:「始」(必需) 学习目标「末」,\ncurrent_level:「始」(可选) 当前水平：beginner、intermediate、advanced。默认：beginner「末」,\ntime_available:「始」(可选) 可用时间，如\"2小时/天\"或\"10小时/周\"。默认：\"2小时/天\"「末」,\npreferred_style:「始」(可选) 学习风格：visual、auditory、kinesthetic、reading。默认：visual「末」,\ndeadline:「始」(可选) 目标完成时间「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：基于学习目标、当前水平和可用时间，生成个性化的学习路径和里程碑规划。"}, {"command": "track_progress", "description": "跟踪和记录学习进度。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」track_progress「末」,\ntopic:「始」(必需) 学习主题「末」,\nstudy_time:「始」(可选) 学习时长（分钟）。默认：0「末」,\ncomprehension_level:「始」(可选) 理解程度：1-10。默认：5「末」,\nnotes:「始」(可选) 学习笔记或心得「末」,\ndifficulties:「始」(可选) 遇到的困难「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：记录学习进度，分析理解程度，提供个性化的学习建议和改进方向。"}, {"command": "generate_quiz", "description": "基于学习内容生成测试题目。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」generate_quiz「末」,\ntopic:「始」(必需) 测试主题「末」,\ncontent:「始」(可选) 基于的学习内容「末」,\nquestion_count:「始」(可选) 题目数量。默认：10「末」,\nquestion_types:「始」(可选) 题目类型：multiple_choice、true_false、short_answer、essay。默认：multiple_choice「末」,\ndifficulty:「始」(可选) 难度级别：easy、medium、hard。默认：medium「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：智能生成测试题目，支持多种题型和难度级别，帮助检验学习效果。"}, {"command": "review_schedule", "description": "基于艾宾浩斯遗忘曲线制定复习计划。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」review_schedule「末」,\ntopic:「始」(必需) 需要复习的主题「末」,\nlast_review_date:「始」(可选) 上次复习日期，格式：YYYY-MM-DD「末」,\nmastery_level:「始」(可选) 掌握程度：1-10。默认：5「末」,\nimportance:「始」(可选) 重要程度：low、medium、high。默认：medium「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：基于遗忘曲线科学制定复习计划，优化记忆保持效果。"}, {"command": "analyze_learning_style", "description": "分析个人学习风格和习惯。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」analyze_learning_style「末」,\nlearning_history:「始」(必需) 学习历史数据或描述「末」,\npreferences:「始」(可选) 学习偏好描述「末」,\nstrengths:「始」(可选) 学习优势「末」,\nweaknesses:「始」(可选) 学习弱点「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：分析学习风格，提供个性化的学习方法建议。"}, {"command": "create_study_plan", "description": "创建详细的学习计划。\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」SmartLearningManager「末」,\ncommand:「始」create_study_plan「末」,\nsubject:「始」(必需) 学习科目「末」,\nduration:「始」(必需) 学习周期，如\"4周\"、\"3个月\"「末」,\ndaily_time:「始」(可选) 每日学习时间。默认：\"2小时\"「末」,\ngoals:「始」(可选) 具体学习目标「末」,\nresources:「始」(可选) 可用学习资源「末」\n<<<[END_TOOL_REQUEST]>>>\n\n功能：制定详细的学习计划，包括时间安排、里程碑设置和资源配置。"}]}}