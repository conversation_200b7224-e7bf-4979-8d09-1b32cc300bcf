# OpenAI 兼容图像生成插件配置文件
# 请复制此文件为 config.env 并填写您的实际配置

# ===== 必需配置 =====
# OpenAI API 密钥
OPENAI_API_KEY=your_openai_api_key_here

# OpenAI API 基础URL（不包含 /v1/images/generations 部分）
# 例如：https://api.openai.com 或您的自定义API地址
OPENAI_API_URL=https://api.openai.com

# ===== 可选配置 =====
# 使用的图像生成模型名称
OPENAI_IMAGE_MODEL=dall-e-3

# 默认图像尺寸
# 可选值：1024x1024, 1792x1024, 1024x1792
DEFAULT_IMAGE_SIZE=1024x1024

# 默认图像质量
# 可选值：standard, hd
DEFAULT_IMAGE_QUALITY=standard

# 默认图像风格
# 可选值：vivid, natural
DEFAULT_IMAGE_STYLE=vivid

# API请求最大重试次数
MAX_RETRIES=3

# 是否将生成的图像保存到本地
# 可选值：true, false
SAVE_IMAGES_LOCALLY=true

# 本地图像保存路径（相对于项目根目录）
LOCAL_SAVE_PATH=image/openai_generated

# 调试模式
# 可选值：true, false
DebugMode=false
