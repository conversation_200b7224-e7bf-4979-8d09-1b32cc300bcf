<!DOCTYPE html>
<html>
<head>
    <title>VCP Chrome Control</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: sans-serif;
            width: 250px;
            padding: 10px;
            text-align: center;
        }
        #status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            margin-top: 15px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #settings {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #ccc;
            display: none; /* Initially hidden */
        }
        .input-group {
            margin-bottom: 10px;
            text-align: left;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
        }
        .input-group input {
            width: 95%;
            padding: 5px;
        }
        #settings-toggle {
            font-size: 12px;
            background: none;
            border: none;
            color: #007bff;
            cursor: pointer;
            text-decoration: underline;
            padding: 0;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h3>VCP Chrome Control</h3>
    <div id="status">正在检查连接状态...</div>
    <button id="toggleConnection">连接</button>
    <button id="settings-toggle">设置</button>

    <div id="settings">
        <div class="input-group">
            <label for="serverUrl">服务器地址 (WebSocket URL):</label>
            <input type="text" id="serverUrl" placeholder="ws://localhost:8088">
        </div>
        <div class="input-group">
            <label for="vcpKey">VCP 密钥:</label>
            <input type="text" id="vcpKey" placeholder="your_secret_key">
        </div>
        <button id="saveSettings">保存设置</button>
    </div>

    <script src="popup.js"></script>
</body>
</html>