let lastPageContent = '';
let vcpIdCounter = 0;
let isContentScriptActive = true;
let sendMessageRetryCount = 0;
const maxRetryCount = 3;

// 获取页面摘要信息
function getPageSummary() {
    try {
        let summary = [];

        // 统计可交互元素
        const interactiveElements = {
            links: document.querySelectorAll('a[href]').length,
            buttons: document.querySelectorAll('button, input[type="button"], input[type="submit"]').length,
            inputs: document.querySelectorAll('input:not([type="button"]):not([type="submit"]), textarea, select').length,
            forms: document.querySelectorAll('form').length
        };

        // 添加元素统计
        const elementCounts = [];
        if (interactiveElements.links > 0) elementCounts.push(`${interactiveElements.links}个链接`);
        if (interactiveElements.buttons > 0) elementCounts.push(`${interactiveElements.buttons}个按钮`);
        if (interactiveElements.inputs > 0) elementCounts.push(`${interactiveElements.inputs}个输入框`);
        if (interactiveElements.forms > 0) elementCounts.push(`${interactiveElements.forms}个表单`);

        if (elementCounts.length > 0) {
            summary.push(`页面包含: ${elementCounts.join(', ')}`);
        }

        // 检测页面类型
        const pageTypes = [];
        if (document.querySelector('form[action*="search"], input[type="search"], [role="search"]')) {
            pageTypes.push('搜索页面');
        }
        if (document.querySelector('form[action*="login"], input[type="password"]')) {
            pageTypes.push('登录页面');
        }
        if (document.querySelector('article, [role="article"], .article, .post')) {
            pageTypes.push('文章页面');
        }
        if (document.querySelector('.product, [data-product], .item, .listing')) {
            pageTypes.push('商品/列表页面');
        }

        if (pageTypes.length > 0) {
            summary.push(`页面类型: ${pageTypes.join(', ')}`);
        }

        // 检测滚动状态
        const scrollInfo = [];
        if (document.body.scrollHeight > window.innerHeight) {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            scrollInfo.push(`可滚动页面 (当前位置: ${scrollPercent}%)`);
        }

        if (scrollInfo.length > 0) {
            summary.push(scrollInfo.join(', '));
        }

        return summary.length > 0 ? summary.join('\n') : null;

    } catch (error) {
        console.warn('Failed to generate page summary:', error);
        return null;
    }
}

function isInteractive(node) {
    if (node.nodeType !== Node.ELEMENT_NODE) {
        return false;
    }
    // 如果元素不可见，则它不是可交互的
    const style = window.getComputedStyle(node);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0' || style.height === '0' || style.width === '0') {
        return false;
    }

    const tagName = node.tagName.toLowerCase();
    const role = node.getAttribute('role');

    // 1. 标准的可交互元素
    if (['a', 'button', 'input', 'textarea', 'select', 'option'].includes(tagName)) {
        return true;
    }

    // 2. 常见的可交互ARIA角色
    if (role && ['button', 'link', 'checkbox', 'radio', 'menuitem', 'tab', 'switch', 'option', 'treeitem', 'searchbox', 'textbox', 'combobox'].includes(role)) {
        return true;
    }

    // 3. 通过JS属性明确可点击
    if (node.hasAttribute('onclick')) {
        return true;
    }

    // 4. 可聚焦的元素（非禁用）
    if (node.hasAttribute('tabindex') && node.getAttribute('tabindex') !== '-1') {
        return true;
    }
    
    // 5. 样式上被设计为可交互的元素
    if (style.cursor === 'pointer') {
        // 避免标记body或仅用于包裹的巨大容器
        if (tagName === 'body' || tagName === 'html') return false;
        // 如果一个元素没有文本内容但有子元素，它可能只是一个包装器
        if ((node.innerText || '').trim().length === 0 && node.children.length > 0) {
             // 但如果这个包装器有role属性，它可能是一个自定义组件
             if (!role) return false;
        }
        return true;
    }

    return false;
}


function pageToMarkdown() {
    try {
        // 检查文档状态
        if (!document || !document.body) {
            console.warn('Document or body not available');
            return '';
        }

        // 为确保每次都是全新的抓取，先移除所有旧的vcp-id
        try {
            document.querySelectorAll('[vcp-id]').forEach(el => el.removeAttribute('vcp-id'));
        } catch (e) {
            console.warn('Failed to remove old vcp-id attributes:', e);
        }

        vcpIdCounter = 0; // 重置计数器
        const body = document.body;

        let markdown = `# ${document.title}\nURL: ${document.URL}\n\n`;

        // 添加页面摘要信息
        const pageInfo = getPageSummary();
        if (pageInfo) {
            markdown += `## 页面信息\n${pageInfo}\n\n`;
        }

        const ignoredTags = ['SCRIPT', 'STYLE', 'NAV', 'FOOTER', 'ASIDE', 'IFRAME', 'NOSCRIPT'];
        const processedNodes = new WeakSet(); // 记录已处理过的节点，防止重复

        function processNode(node) {
            // 1. 基本过滤条件
            if (!node || processedNodes.has(node)) return '';

            if (node.nodeType === Node.ELEMENT_NODE) {
                const style = window.getComputedStyle(node);
                if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                    return '';
                }
                if (ignoredTags.includes(node.tagName)) {
                    return '';
                }
            }

            // 如果父元素已经被标记为可交互元素并处理过，则跳过此节点
            if (node.parentElement && node.parentElement.closest('[vcp-id]')) {
                return '';
            }

            // 2. 优先处理可交互元素
            if (isInteractive(node)) {
                const interactiveMd = formatInteractiveElement(node);
                if (interactiveMd) {
                    // 标记此节点及其所有子孙节点为已处理
                    processedNodes.add(node);
                    node.querySelectorAll('*').forEach(child => processedNodes.add(child));
                    return interactiveMd + '\n';
                }
            }

            // 3. 处理文本节点
            if (node.nodeType === Node.TEXT_NODE) {
                // 用正则表达式替换多个空白为一个空格
                return node.textContent.replace(/\s+/g, ' ').trim() + ' ';
            }

            // 4. 递归处理子节点 (包括 Shadow DOM)
            let childContent = '';
            if (node.shadowRoot) {
                childContent += processNode(node.shadowRoot);
            }
            
            node.childNodes.forEach(child => {
                childContent += processNode(child);
            });

            // 5. 为块级元素添加换行以保持结构
            if (node.nodeType === Node.ELEMENT_NODE && childContent.trim()) {
                const style = window.getComputedStyle(node);
                if (style.display === 'block' || style.display === 'flex' || style.display === 'grid') {
                    return '\n' + childContent.trim() + '\n';
                }
            }

            return childContent;
        }

        markdown += processNode(body);
        
        // 清理最终的Markdown文本
        markdown = markdown.replace(/[ \t]+/g, ' '); // 合并多余空格
        markdown = markdown.replace(/ (\n)/g, '\n'); // 清理行尾空格
        markdown = markdown.replace(/(\n\s*){3,}/g, '\n\n'); // 合并多余空行
        markdown = markdown.trim();
        
        return markdown;
    } catch (e) {
        return `# ${document.title}\n\n[处理页面时出错: ${e.message}]`;
    }
}


function formatInteractiveElement(el) {
    // 避免重复标记同一个元素
    if (el.hasAttribute('vcp-id')) {
        return '';
    }

    vcpIdCounter++;
    const vcpId = `vcp-id-${vcpIdCounter}`;
    el.setAttribute('vcp-id', vcpId);

    let text = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
    const tagName = el.tagName.toLowerCase();
    const role = el.getAttribute('role');

    if (role === 'combobox' || role === 'searchbox') {
        const label = findLabelForInput(el);
        return `[输入框: ${label || text || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }

    if (tagName === 'a' && el.href) {
        // 增强链接信息，包含更多上下文
        let linkText = text || el.getAttribute('title') || el.getAttribute('aria-label') || '无标题链接';

        // 如果链接文本为空但包含图片，尝试获取图片alt文本
        if (!text && el.querySelector('img')) {
            const img = el.querySelector('img');
            linkText = img.alt || img.title || '图片链接';
        }

        // 截断过长的文本
        if (linkText.length > 50) {
            linkText = linkText.substring(0, 47) + '...';
        }

        return `[链接: ${linkText}](${vcpId})`;
    }

    if (tagName === 'button' || role === 'button' || (tagName === 'input' && ['button', 'submit', 'reset'].includes(el.type))) {
        return `[按钮: ${text || '无标题按钮'}](${vcpId})`;
    }

    if (tagName === 'input' && !['button', 'submit', 'reset', 'hidden'].includes(el.type)) {
        const label = findLabelForInput(el);
        return `[输入框: ${label || text || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }

    if (tagName === 'textarea') {
        const label = findLabelForInput(el);
        return `[文本区域: ${label || text || el.name || el.id || '无标题文本区域'}](${vcpId})`;
    }

    if (tagName === 'select') {
        const label = findLabelForInput(el);
        return `[下拉选择: ${label || text || el.name || el.id || '无标题下拉框'}](${vcpId})`;
    }

    // 为其他所有可交互元素（如可点击的div，带角色的span等）提供通用处理
    if (text) {
        return `[可交互元素: ${text}](${vcpId})`;
    }

    // 如果元素没有文本但仍然是可交互的（例如，一个图标按钮），我们仍然需要标记它
    // 但我们不回退ID，而是将其标记为一个没有文本的元素
    const type = el.type || role || tagName;
    return `[可交互元素: 无文本 (${type})](${vcpId})`;
}

function findLabelForInput(inputElement) {
    if (inputElement.id) {
        const label = document.querySelector(`label[for="${inputElement.id}"]`);
        if (label) return label.innerText.trim();
    }
    const parentLabel = inputElement.closest('label');
    if (parentLabel) return parentLabel.innerText.trim();
    return null;
}

// 处理滚动命令
function handleScrollCommand(commandData, result) {
    const { direction, distance, target } = commandData;

    try {
        let scrollElement = window;
        let scrollOptions = { behavior: 'smooth' };

        // 如果指定了目标元素，尝试找到它
        if (target && target !== 'page') {
            const element = document.querySelector(`[vcp-id="${target}"]`) ||
                           document.querySelector(target) ||
                           document.getElementById(target) ||
                           document.querySelector(`[class*="${target}"]`);

            if (element) {
                scrollElement = element;
                // 检查元素是否可滚动
                const style = window.getComputedStyle(element);
                if (style.overflow === 'auto' || style.overflow === 'scroll' ||
                    style.overflowY === 'auto' || style.overflowY === 'scroll') {
                    // 元素本身可滚动
                } else {
                    // 滚动到元素位置
                    element.scrollIntoView(scrollOptions);
                    result.status = 'success';
                    result.message = `成功滚动到元素 '${target}'`;
                    return;
                }
            }
        }

        // 计算滚动距离
        let scrollDistance = 0;
        if (distance) {
            scrollDistance = parseInt(distance);
        } else {
            // 默认滚动距离为视窗高度的一半
            scrollDistance = window.innerHeight / 2;
        }

        // 根据方向执行滚动
        switch (direction?.toLowerCase()) {
            case 'up':
            case 'top':
                if (scrollElement === window) {
                    window.scrollBy({ top: -scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ top: -scrollDistance, ...scrollOptions });
                }
                break;

            case 'down':
            case 'bottom':
                if (scrollElement === window) {
                    window.scrollBy({ top: scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ top: scrollDistance, ...scrollOptions });
                }
                break;

            case 'left':
                if (scrollElement === window) {
                    window.scrollBy({ left: -scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ left: -scrollDistance, ...scrollOptions });
                }
                break;

            case 'right':
                if (scrollElement === window) {
                    window.scrollBy({ left: scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ left: scrollDistance, ...scrollOptions });
                }
                break;

            case 'totop':
                if (scrollElement === window) {
                    window.scrollTo({ top: 0, ...scrollOptions });
                } else {
                    scrollElement.scrollTo({ top: 0, ...scrollOptions });
                }
                break;

            case 'tobottom':
                if (scrollElement === window) {
                    window.scrollTo({ top: document.body.scrollHeight, ...scrollOptions });
                } else {
                    scrollElement.scrollTo({ top: scrollElement.scrollHeight, ...scrollOptions });
                }
                break;

            default:
                throw new Error(`不支持的滚动方向: ${direction}。支持的方向: up, down, left, right, totop, tobottom`);
        }

        result.status = 'success';
        result.message = `成功执行滚动操作: ${direction}${distance ? ` (距离: ${distance}px)` : ''}`;

    } catch (error) {
        result.status = 'error';
        result.error = `滚动操作失败: ${error.message}`;
    }
}

// 获取页面所有链接
function getAllLinks() {
    try {
        const links = [];
        const linkElements = document.querySelectorAll('a[href]');

        linkElements.forEach((link, index) => {
            // 检查链接是否可见
            const style = window.getComputedStyle(link);
            if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                return;
            }

            // 获取链接文本
            let text = (link.innerText || link.textContent || '').trim();

            // 如果没有文本但有图片，获取图片alt
            if (!text && link.querySelector('img')) {
                const img = link.querySelector('img');
                text = img.alt || img.title || '图片链接';
            }

            // 如果还是没有文本，使用其他属性
            if (!text) {
                text = link.getAttribute('title') ||
                       link.getAttribute('aria-label') ||
                       `链接${index + 1}`;
            }

            // 截断过长的文本
            if (text.length > 100) {
                text = text.substring(0, 97) + '...';
            }

            // 分配vcp-id
            if (!link.hasAttribute('vcp-id')) {
                vcpIdCounter++;
                const vcpId = `vcp-id-${vcpIdCounter}`;
                link.setAttribute('vcp-id', vcpId);
            }

            links.push({
                id: link.getAttribute('vcp-id'),
                text: text,
                href: link.href,
                target: link.target || '_self'
            });
        });

        return {
            status: 'success',
            message: `找到 ${links.length} 个链接`,
            data: {
                count: links.length,
                links: links
            }
        };

    } catch (error) {
        return {
            status: 'error',
            error: `获取链接失败: ${error.message}`
        };
    }
}

function sendPageInfoUpdate() {
    if (!isContentScriptActive) return;

    const currentPageContent = pageToMarkdown();
    if (currentPageContent && currentPageContent !== lastPageContent) {
        lastPageContent = currentPageContent;
        sendMessageWithRetry({
            type: 'PAGE_INFO_UPDATE',
            data: { markdown: currentPageContent }
        });
    }
}

// 带重试机制的消息发送
function sendMessageWithRetry(message, retryCount = 0) {
    if (!isContentScriptActive || retryCount >= maxRetryCount) {
        return;
    }

    try {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                const error = chrome.runtime.lastError.message;
                console.log(`Message send failed (attempt ${retryCount + 1}):`, error);

                // 如果是上下文失效错误，停止重试
                if (error.includes("Extension context invalidated") ||
                    error.includes("Could not establish connection")) {
                    console.log("Content script context invalidated, stopping operations");
                    isContentScriptActive = false;
                    return;
                }

                // 其他错误，延迟重试
                setTimeout(() => {
                    sendMessageWithRetry(message, retryCount + 1);
                }, 1000 * (retryCount + 1)); // 递增延迟
            }
        });
    } catch (error) {
        console.error("Failed to send message:", error);
        isContentScriptActive = false;
    }
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'CLEAR_STATE') {
        lastPageContent = '';
    } else if (request.type === 'REQUEST_PAGE_INFO_UPDATE') {
        sendPageInfoUpdate();
    } else if (request.type === 'EXECUTE_COMMAND') {
        const { command, target, text, requestId, sourceClientId } = request.data;
        let result = {};

        try {
            let element = document.querySelector(`[vcp-id="${target}"]`);

            if (!element) {
                const allInteractiveElements = document.querySelectorAll('[vcp-id]');
                for (const el of allInteractiveElements) {
                    const elText = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
                    if (elText === target) {
                        element = el;
                        break;
                    }
                }
            }

            if (!element) {
                throw new Error(`未能在页面上找到目标为 '${target}' 的元素。`);
            }

            if (command === 'type') {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.value = text;
                    result = { status: 'success', message: `成功在ID为 '${target}' 的元素中输入文本。` };
                } else {
                    throw new Error(`ID为 '${target}' 的元素不是一个输入框。`);
                }
            } else if (command === 'click') {
                // 模拟真实用户点击，这对于处理使用现代前端框架（如React, Vue）构建的页面至关重要
                element.focus(); // 首先聚焦元素
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(clickEvent);
                result = { status: 'success', message: `成功点击了ID为 '${target}' 的元素。` };
            } else if (command === 'scroll') {
                // 处理滚动命令
                handleScrollCommand(request.data, result);
            } else if (command === 'get_links') {
                // 获取页面所有链接
                result = getAllLinks();
            } else {
                throw new Error(`不支持的命令: ${command}`);
            }
        } catch (error) {
            result = { status: 'error', error: error.message };
        }

        sendMessageWithRetry({
            type: 'COMMAND_RESULT',
            data: {
                requestId,
                sourceClientId,
                ...result
            }
        });
        setTimeout(sendPageInfoUpdate, 500);
    }
});

const debouncedSendPageInfoUpdate = debounce(sendPageInfoUpdate, 500); // 降低延迟，提高响应速度

const observer = new MutationObserver((mutations) => {
    debouncedSendPageInfoUpdate();
});
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    characterData: true
});

document.addEventListener('click', debouncedSendPageInfoUpdate);
document.addEventListener('focusin', debouncedSendPageInfoUpdate);
document.addEventListener('scroll', debouncedSendPageInfoUpdate, true); // 监听滚动事件

// 页面加载完成时发送更新
if (document.readyState === 'loading') {
    window.addEventListener('load', sendPageInfoUpdate);
} else {
    // 如果页面已经加载完成，立即发送更新
    setTimeout(sendPageInfoUpdate, 100);
}

// 页面可见性变化时发送更新
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible' && isContentScriptActive) {
        setTimeout(sendPageInfoUpdate, 100); // 短暂延迟确保页面完全可见
    }
});

// 定期发送更新，但只在页面可见时
setInterval(() => {
    if (document.visibilityState === 'visible' && isContentScriptActive) {
        sendPageInfoUpdate();
    }
}, 5000);

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}