let lastPageContent = '';
let vcpIdCounter = 0;
let isContentScriptActive = true;
let sendMessageRetryCount = 0;
const maxRetryCount = 3;

// 获取页面摘要信息
function getPageSummary() {
    try {
        let summary = [];

        // 统计可交互元素
        const interactiveElements = {
            links: document.querySelectorAll('a[href]').length,
            buttons: document.querySelectorAll('button, input[type="button"], input[type="submit"]').length,
            inputs: document.querySelectorAll('input:not([type="button"]):not([type="submit"]), textarea, select').length,
            forms: document.querySelectorAll('form').length
        };

        // 添加元素统计
        const elementCounts = [];
        if (interactiveElements.links > 0) elementCounts.push(`${interactiveElements.links}个链接`);
        if (interactiveElements.buttons > 0) elementCounts.push(`${interactiveElements.buttons}个按钮`);
        if (interactiveElements.inputs > 0) elementCounts.push(`${interactiveElements.inputs}个输入框`);
        if (interactiveElements.forms > 0) elementCounts.push(`${interactiveElements.forms}个表单`);

        if (elementCounts.length > 0) {
            summary.push(`页面包含: ${elementCounts.join(', ')}`);
        }

        // 检测页面类型
        const pageTypes = [];
        if (document.querySelector('form[action*="search"], input[type="search"], [role="search"]')) {
            pageTypes.push('搜索页面');
        }
        if (document.querySelector('form[action*="login"], input[type="password"]')) {
            pageTypes.push('登录页面');
        }
        if (document.querySelector('article, [role="article"], .article, .post')) {
            pageTypes.push('文章页面');
        }
        if (document.querySelector('.product, [data-product], .item, .listing')) {
            pageTypes.push('商品/列表页面');
        }

        if (pageTypes.length > 0) {
            summary.push(`页面类型: ${pageTypes.join(', ')}`);
        }

        // 检测滚动状态
        const scrollInfo = [];
        if (document.body.scrollHeight > window.innerHeight) {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            scrollInfo.push(`可滚动页面 (当前位置: ${scrollPercent}%)`);
        }

        if (scrollInfo.length > 0) {
            summary.push(scrollInfo.join(', '));
        }

        return summary.length > 0 ? summary.join('\n') : null;

    } catch (error) {
        console.warn('Failed to generate page summary:', error);
        return null;
    }
}

function isInteractive(node) {
    if (node.nodeType !== Node.ELEMENT_NODE) {
        return false;
    }
    // 如果元素不可见，则它不是可交互的
    const style = window.getComputedStyle(node);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0' || style.height === '0' || style.width === '0') {
        return false;
    }

    const tagName = node.tagName.toLowerCase();
    const role = node.getAttribute('role');

    // 1. 标准的可交互元素
    if (['a', 'button', 'input', 'textarea', 'select', 'option'].includes(tagName)) {
        return true;
    }

    // 2. 常见的可交互ARIA角色
    if (role && ['button', 'link', 'checkbox', 'radio', 'menuitem', 'tab', 'switch', 'option', 'treeitem', 'searchbox', 'textbox', 'combobox'].includes(role)) {
        return true;
    }

    // 3. 通过JS属性明确可点击
    if (node.hasAttribute('onclick')) {
        return true;
    }

    // 4. 可聚焦的元素（非禁用）
    if (node.hasAttribute('tabindex') && node.getAttribute('tabindex') !== '-1') {
        return true;
    }
    
    // 5. 样式上被设计为可交互的元素
    if (style.cursor === 'pointer') {
        // 避免标记body或仅用于包裹的巨大容器
        if (tagName === 'body' || tagName === 'html') return false;
        // 如果一个元素没有文本内容但有子元素，它可能只是一个包装器
        if ((node.innerText || '').trim().length === 0 && node.children.length > 0) {
             // 但如果这个包装器有role属性，它可能是一个自定义组件
             if (!role) return false;
        }
        return true;
    }

    return false;
}


function pageToMarkdown() {
    try {
        // 检查文档状态
        if (!document || !document.body) {
            console.warn('Document or body not available');
            return '';
        }

        // 为确保每次都是全新的抓取，先移除所有旧的vcp-id
        try {
            document.querySelectorAll('[vcp-id]').forEach(el => el.removeAttribute('vcp-id'));
        } catch (e) {
            console.warn('Failed to remove old vcp-id attributes:', e);
        }

        vcpIdCounter = 0; // 重置计数器
        const body = document.body;

        let markdown = `# ${document.title}\nURL: ${document.URL}\n\n`;

        // 添加页面摘要信息
        const pageInfo = getPageSummary();
        if (pageInfo) {
            markdown += `## 页面信息\n${pageInfo}\n\n`;
        }

        const ignoredTags = ['SCRIPT', 'STYLE', 'NAV', 'FOOTER', 'ASIDE', 'IFRAME', 'NOSCRIPT'];
        const processedNodes = new WeakSet(); // 记录已处理过的节点，防止重复

        function processNode(node) {
            // 1. 基本过滤条件
            if (!node || processedNodes.has(node)) return '';

            if (node.nodeType === Node.ELEMENT_NODE) {
                const style = window.getComputedStyle(node);
                if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                    return '';
                }
                if (ignoredTags.includes(node.tagName)) {
                    return '';
                }
            }

            // 如果父元素已经被标记为可交互元素并处理过，则跳过此节点
            if (node.parentElement && node.parentElement.closest('[vcp-id]')) {
                return '';
            }

            // 2. 优先处理可交互元素
            if (isInteractive(node)) {
                const interactiveMd = formatInteractiveElement(node);
                if (interactiveMd) {
                    // 标记此节点及其所有子孙节点为已处理
                    processedNodes.add(node);
                    node.querySelectorAll('*').forEach(child => processedNodes.add(child));
                    return interactiveMd + '\n';
                }
            }

            // 3. 处理文本节点
            if (node.nodeType === Node.TEXT_NODE) {
                // 用正则表达式替换多个空白为一个空格
                return node.textContent.replace(/\s+/g, ' ').trim() + ' ';
            }

            // 4. 递归处理子节点 (包括 Shadow DOM)
            let childContent = '';
            if (node.shadowRoot) {
                childContent += processNode(node.shadowRoot);
            }
            
            node.childNodes.forEach(child => {
                childContent += processNode(child);
            });

            // 5. 为块级元素添加换行以保持结构
            if (node.nodeType === Node.ELEMENT_NODE && childContent.trim()) {
                const style = window.getComputedStyle(node);
                if (style.display === 'block' || style.display === 'flex' || style.display === 'grid') {
                    return '\n' + childContent.trim() + '\n';
                }
            }

            return childContent;
        }

        markdown += processNode(body);
        
        // 清理最终的Markdown文本
        markdown = markdown.replace(/[ \t]+/g, ' '); // 合并多余空格
        markdown = markdown.replace(/ (\n)/g, '\n'); // 清理行尾空格
        markdown = markdown.replace(/(\n\s*){3,}/g, '\n\n'); // 合并多余空行
        markdown = markdown.trim();
        
        return markdown;
    } catch (e) {
        return `# ${document.title}\n\n[处理页面时出错: ${e.message}]`;
    }
}


function formatInteractiveElement(el) {
    // 避免重复标记同一个元素
    if (el.hasAttribute('vcp-id')) {
        return '';
    }

    vcpIdCounter++;
    const vcpId = `vcp-id-${vcpIdCounter}`;
    el.setAttribute('vcp-id', vcpId);

    let text = (el.innerText || el.value || el.placeholder || el.ariaLabel || el.title || '').trim().replace(/\s+/g, ' ');
    const tagName = el.tagName.toLowerCase();
    const role = el.getAttribute('role');

    if (role === 'combobox' || role === 'searchbox') {
        const label = findLabelForInput(el);
        return `[输入框: ${label || text || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }

    if (tagName === 'a' && el.href) {
        // 增强链接信息，包含更多上下文
        let linkText = text || el.getAttribute('title') || el.getAttribute('aria-label') || '无标题链接';

        // 如果链接文本为空但包含图片，尝试获取图片alt文本
        if (!text && el.querySelector('img')) {
            const img = el.querySelector('img');
            linkText = img.alt || img.title || '图片链接';
        }

        // 截断过长的文本
        if (linkText.length > 50) {
            linkText = linkText.substring(0, 47) + '...';
        }

        return `[链接: ${linkText}](${vcpId})`;
    }

    if (tagName === 'button' || role === 'button' || (tagName === 'input' && ['button', 'submit', 'reset'].includes(el.type))) {
        return `[按钮: ${text || '无标题按钮'}](${vcpId})`;
    }

    if (tagName === 'input' && !['button', 'submit', 'reset', 'hidden'].includes(el.type)) {
        const label = findLabelForInput(el);
        let inputType = '输入框';

        // 根据输入框类型和属性提供更具体的描述
        if (el.type === 'search' || el.name?.includes('search') || el.placeholder?.includes('搜索') || el.placeholder?.includes('search')) {
            inputType = '搜索框';
        } else if (el.type === 'email') {
            inputType = '邮箱输入框';
        } else if (el.type === 'password') {
            inputType = '密码输入框';
        } else if (el.type === 'tel') {
            inputType = '电话输入框';
        } else if (el.type === 'url') {
            inputType = 'URL输入框';
        }

        return `[${inputType}: ${label || text || el.placeholder || el.name || el.id || '无标题输入框'}](${vcpId})`;
    }

    if (tagName === 'textarea') {
        const label = findLabelForInput(el);
        return `[文本区域: ${label || text || el.placeholder || el.name || el.id || '无标题文本区域'}](${vcpId})`;
    }

    if (tagName === 'select') {
        const label = findLabelForInput(el);
        return `[下拉选择: ${label || text || el.name || el.id || '无标题下拉框'}](${vcpId})`;
    }

    // 为其他所有可交互元素（如可点击的div，带角色的span等）提供通用处理
    if (text) {
        return `[可交互元素: ${text}](${vcpId})`;
    }

    // 如果元素没有文本但仍然是可交互的（例如，一个图标按钮），我们仍然需要标记它
    // 但我们不回退ID，而是将其标记为一个没有文本的元素
    const type = el.type || role || tagName;
    return `[可交互元素: 无文本 (${type})](${vcpId})`;
}

function findLabelForInput(inputElement) {
    if (inputElement.id) {
        const label = document.querySelector(`label[for="${inputElement.id}"]`);
        if (label) return label.innerText.trim();
    }
    const parentLabel = inputElement.closest('label');
    if (parentLabel) return parentLabel.innerText.trim();
    return null;
}

// 处理滚动命令
function handleScrollCommand(commandData, result) {
    const { direction, distance, target } = commandData;

    try {
        let scrollElement = window;
        let scrollOptions = { behavior: 'smooth' };

        // 如果指定了目标元素，尝试找到它
        if (target && target !== 'page') {
            const element = document.querySelector(`[vcp-id="${target}"]`) ||
                           document.querySelector(target) ||
                           document.getElementById(target) ||
                           document.querySelector(`[class*="${target}"]`);

            if (element) {
                scrollElement = element;
                // 检查元素是否可滚动
                const style = window.getComputedStyle(element);
                if (style.overflow === 'auto' || style.overflow === 'scroll' ||
                    style.overflowY === 'auto' || style.overflowY === 'scroll') {
                    // 元素本身可滚动
                } else {
                    // 滚动到元素位置
                    element.scrollIntoView(scrollOptions);
                    result.status = 'success';
                    result.message = `成功滚动到元素 '${target}'`;
                    return;
                }
            }
        }

        // 计算滚动距离
        let scrollDistance = 0;
        if (distance) {
            scrollDistance = parseInt(distance);
        } else {
            // 默认滚动距离为视窗高度的一半
            scrollDistance = window.innerHeight / 2;
        }

        // 根据方向执行滚动
        switch (direction?.toLowerCase()) {
            case 'up':
            case 'top':
                if (scrollElement === window) {
                    window.scrollBy({ top: -scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ top: -scrollDistance, ...scrollOptions });
                }
                break;

            case 'down':
            case 'bottom':
                if (scrollElement === window) {
                    window.scrollBy({ top: scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ top: scrollDistance, ...scrollOptions });
                }
                break;

            case 'left':
                if (scrollElement === window) {
                    window.scrollBy({ left: -scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ left: -scrollDistance, ...scrollOptions });
                }
                break;

            case 'right':
                if (scrollElement === window) {
                    window.scrollBy({ left: scrollDistance, ...scrollOptions });
                } else {
                    scrollElement.scrollBy({ left: scrollDistance, ...scrollOptions });
                }
                break;

            case 'totop':
                if (scrollElement === window) {
                    window.scrollTo({ top: 0, ...scrollOptions });
                } else {
                    scrollElement.scrollTo({ top: 0, ...scrollOptions });
                }
                break;

            case 'tobottom':
                if (scrollElement === window) {
                    window.scrollTo({ top: document.body.scrollHeight, ...scrollOptions });
                } else {
                    scrollElement.scrollTo({ top: scrollElement.scrollHeight, ...scrollOptions });
                }
                break;

            default:
                throw new Error(`不支持的滚动方向: ${direction}。支持的方向: up, down, left, right, totop, tobottom`);
        }

        result.status = 'success';
        result.message = `成功执行滚动操作: ${direction}${distance ? ` (距离: ${distance}px)` : ''}`;

    } catch (error) {
        result.status = 'error';
        result.error = `滚动操作失败: ${error.message}`;
    }
}

// 获取页面所有链接
function getAllLinks() {
    try {
        const links = [];
        const linkElements = document.querySelectorAll('a[href]');

        linkElements.forEach((link, index) => {
            // 检查链接是否可见
            const style = window.getComputedStyle(link);
            if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                return;
            }

            // 获取链接文本
            let text = (link.innerText || link.textContent || '').trim();

            // 如果没有文本但有图片，获取图片alt
            if (!text && link.querySelector('img')) {
                const img = link.querySelector('img');
                text = img.alt || img.title || '图片链接';
            }

            // 如果还是没有文本，使用其他属性
            if (!text) {
                text = link.getAttribute('title') ||
                       link.getAttribute('aria-label') ||
                       `链接${index + 1}`;
            }

            // 截断过长的文本
            if (text.length > 100) {
                text = text.substring(0, 97) + '...';
            }

            // 分配vcp-id
            if (!link.hasAttribute('vcp-id')) {
                vcpIdCounter++;
                const vcpId = `vcp-id-${vcpIdCounter}`;
                link.setAttribute('vcp-id', vcpId);
            }

            links.push({
                id: link.getAttribute('vcp-id'),
                text: text,
                href: link.href,
                target: link.target || '_self'
            });
        });

        return {
            status: 'success',
            message: `找到 ${links.length} 个链接`,
            data: {
                count: links.length,
                links: links
            }
        };

    } catch (error) {
        return {
            status: 'error',
            error: `获取链接失败: ${error.message}`
        };
    }
}

// 获取页面调试信息
function getPageDebugInfo() {
    try {
        const debugInfo = {
            pageInfo: {
                title: document.title,
                url: document.URL,
                readyState: document.readyState,
                visibilityState: document.visibilityState
            },
            elementStats: {
                totalElements: document.querySelectorAll('*').length,
                interactiveElements: document.querySelectorAll('[vcp-id]').length,
                visibleInteractive: 0,
                hiddenInteractive: 0
            },
            elements: []
        };

        // 统计和收集可交互元素信息
        const allInteractiveElements = document.querySelectorAll('[vcp-id]');

        allInteractiveElements.forEach((el, index) => {
            const isVisible = isElementVisible(el);
            if (isVisible) {
                debugInfo.elementStats.visibleInteractive++;
            } else {
                debugInfo.elementStats.hiddenInteractive++;
            }

            const elementInfo = {
                index: index + 1,
                vcpId: el.getAttribute('vcp-id'),
                tagName: el.tagName.toLowerCase(),
                type: el.type || null,
                text: getElementText(el),
                visible: isVisible,
                attributes: {
                    id: el.id || null,
                    name: el.name || null,
                    className: el.className || null,
                    placeholder: el.placeholder || null,
                    title: el.title || null,
                    ariaLabel: el.getAttribute('aria-label') || null
                },
                position: (() => {
                    try {
                        const rect = el.getBoundingClientRect();
                        return {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        };
                    } catch (e) {
                        return null;
                    }
                })()
            };

            debugInfo.elements.push(elementInfo);
        });

        // 按可见性排序，可见的在前
        debugInfo.elements.sort((a, b) => {
            if (a.visible && !b.visible) return -1;
            if (!a.visible && b.visible) return 1;
            return 0;
        });

        return {
            status: 'success',
            message: `页面调试信息获取成功。共 ${debugInfo.elementStats.interactiveElements} 个可交互元素，其中 ${debugInfo.elementStats.visibleInteractive} 个可见。`,
            data: debugInfo
        };

    } catch (error) {
        return {
            status: 'error',
            error: `获取调试信息失败: ${error.message}`
        };
    }
}

// 执行搜索操作
async function performSearch(searchText) {
    try {
        // 查找搜索框的多种策略
        let searchBox = null;

        // 1. 通过input type="search"查找
        searchBox = document.querySelector('input[type="search"]');

        // 2. 通过常见的搜索相关属性查找
        if (!searchBox) {
            const searchSelectors = [
                'input[name*="search"]',
                'input[placeholder*="搜索"]',
                'input[placeholder*="search"]',
                'input[id*="search"]',
                'input[class*="search"]',
                '[role="search"] input',
                '.search-box input',
                '.search-input',
                '#search',
                '.search'
            ];

            for (const selector of searchSelectors) {
                searchBox = document.querySelector(selector);
                if (searchBox && searchBox.tagName === 'INPUT') {
                    break;
                }
            }
        }

        // 3. 查找最可能的文本输入框
        if (!searchBox) {
            const textInputs = document.querySelectorAll('input[type="text"], input:not([type])');
            for (const input of textInputs) {
                if (isElementVisible(input)) {
                    // 检查是否看起来像搜索框
                    const text = getElementText(input).toLowerCase();
                    const placeholder = (input.placeholder || '').toLowerCase();
                    const name = (input.name || '').toLowerCase();
                    const id = (input.id || '').toLowerCase();

                    if (text.includes('搜索') || text.includes('search') ||
                        placeholder.includes('搜索') || placeholder.includes('search') ||
                        name.includes('search') || id.includes('search')) {
                        searchBox = input;
                        break;
                    }
                }
            }
        }

        // 4. 如果还是找不到，使用第一个可见的文本输入框
        if (!searchBox) {
            const textInputs = document.querySelectorAll('input[type="text"], input:not([type])');
            for (const input of textInputs) {
                if (isElementVisible(input)) {
                    searchBox = input;
                    break;
                }
            }
        }

        if (!searchBox) {
            return {
                status: 'error',
                error: '未找到搜索框。页面上没有可用的文本输入框。'
            };
        }

        // 执行搜索
        // 先滚动到搜索框可见位置
        searchBox.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 等待滚动完成
        await new Promise(resolve => setTimeout(resolve, 300));

        // 聚焦并清空
        searchBox.focus();
        searchBox.select(); // 选中所有文本

        // 等待一下确保焦点设置完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 输入文本
        searchBox.value = searchText;

        // 触发输入事件
        searchBox.dispatchEvent(new Event('input', { bubbles: true }));
        searchBox.dispatchEvent(new Event('change', { bubbles: true }));

        // 等待一下让页面处理输入
        await new Promise(resolve => setTimeout(resolve, 200));

        // 尝试提交搜索（按Enter键）
        const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            bubbles: true,
            cancelable: true
        });
        searchBox.dispatchEvent(enterEvent);

        // 也触发keyup事件
        const enterUpEvent = new KeyboardEvent('keyup', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            bubbles: true
        });
        searchBox.dispatchEvent(enterUpEvent);

        // 等待一下再查找搜索按钮
        await new Promise(resolve => setTimeout(resolve, 300));

        // 查找并点击搜索按钮
        let searchButtonClicked = false;
        const searchButtonSelectors = [
            // 表单提交按钮
            'button[type="submit"]',
            'input[type="submit"]',
            // 包含搜索关键词的按钮
            'button[class*="search"]',
            'button[id*="search"]',
            'button[aria-label*="search"]',
            'button[aria-label*="搜索"]',
            // 搜索图标按钮
            'button svg[class*="search"]',
            'button .search-icon',
            'button [class*="search-icon"]',
            // 通用搜索按钮
            '[role="button"][class*="search"]',
            '.search-button',
            '.search-btn',
            '#search-button',
            '#search-btn'
        ];

        // 首先尝试在搜索框附近查找按钮
        const searchForm = searchBox.closest('form');
        if (searchForm) {
            const formButtons = searchForm.querySelectorAll('button, input[type="submit"]');
            for (const button of formButtons) {
                if (isElementVisible(button)) {
                    button.click();
                    searchButtonClicked = true;
                    break;
                }
            }
        }

        // 如果表单内没找到，在整个页面查找
        if (!searchButtonClicked) {
            for (const selector of searchButtonSelectors) {
                const buttons = document.querySelectorAll(selector);
                for (const button of buttons) {
                    if (isElementVisible(button)) {
                        const buttonText = getElementText(button).toLowerCase();
                        if (buttonText.includes('搜索') || buttonText.includes('search') ||
                            buttonText.includes('go') || buttonText === '' ||
                            button.querySelector('svg') || button.querySelector('[class*="icon"]')) {

                            // 点击按钮
                            button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            await new Promise(resolve => setTimeout(resolve, 200));
                            button.click();
                            searchButtonClicked = true;
                            break;
                        }
                    }
                }
                if (searchButtonClicked) break;
            }
        }

        return {
            status: 'success',
            message: `成功在搜索框中输入 "${searchText}" 并尝试提交搜索。${searchButtonClicked ? '已点击搜索按钮。' : '已按Enter键提交。'}`,
            data: {
                searchBox: {
                    tagName: searchBox.tagName,
                    type: searchBox.type,
                    placeholder: searchBox.placeholder,
                    name: searchBox.name,
                    id: searchBox.id,
                    value: searchBox.value
                },
                searchButtonClicked: searchButtonClicked,
                searchMethod: searchButtonClicked ? 'button_click' : 'enter_key'
            }
        };

    } catch (error) {
        return {
            status: 'error',
            error: `搜索操作失败: ${error.message}`
        };
    }
}

// 根据位置选择元素
async function selectByPosition(commandData) {
    try {
        const { type, position, action } = commandData;

        // 获取指定类型的所有元素
        const elements = getElementsByType(type);

        if (elements.length === 0) {
            return {
                status: 'error',
                error: `页面上没有找到类型为 "${type}" 的元素。`
            };
        }

        // 根据位置选择元素
        let selectedElement = null;
        let selectedIndex = -1;

        if (position === 'first' || position === '1') {
            selectedElement = elements[0];
            selectedIndex = 0;
        } else if (position === 'second' || position === '2') {
            selectedElement = elements[1];
            selectedIndex = 1;
        } else if (position === 'third' || position === '3') {
            selectedElement = elements[2];
            selectedIndex = 2;
        } else if (position === 'last') {
            selectedElement = elements[elements.length - 1];
            selectedIndex = elements.length - 1;
        } else if (!isNaN(parseInt(position))) {
            const index = parseInt(position) - 1;
            if (index >= 0 && index < elements.length) {
                selectedElement = elements[index];
                selectedIndex = index;
            }
        }

        if (!selectedElement) {
            return {
                status: 'error',
                error: `无法找到第 "${position}" 个类型为 "${type}" 的元素。共找到 ${elements.length} 个元素。`
            };
        }

        // 执行指定的操作
        switch (action) {
            case 'click':
                // 滚动到元素可见位置
                selectedElement.element.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // 等待滚动完成
                await new Promise(resolve => setTimeout(resolve, 300));

                // 点击元素
                selectedElement.element.focus();
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                selectedElement.element.dispatchEvent(clickEvent);

                return {
                    status: 'success',
                    message: `成功点击了第 ${selectedIndex + 1} 个${type}元素: "${selectedElement.text}"`,
                    data: {
                        element: selectedElement,
                        position: selectedIndex + 1,
                        total: elements.length
                    }
                };

            case 'info':
                return {
                    status: 'success',
                    message: `获取第 ${selectedIndex + 1} 个${type}元素信息`,
                    data: {
                        element: selectedElement,
                        position: selectedIndex + 1,
                        total: elements.length,
                        allElements: elements.map((el, idx) => ({
                            position: idx + 1,
                            text: el.text,
                            url: el.url || null
                        }))
                    }
                };

            default:
                return {
                    status: 'error',
                    error: `不支持的操作: ${action}`
                };
        }

    } catch (error) {
        return {
            status: 'error',
            error: `位置选择操作失败: ${error.message}`
        };
    }
}

// 根据类型获取元素
function getElementsByType(type) {
    const elements = [];

    switch (type.toLowerCase()) {
        case 'video':
        case '视频':
            // 查找视频相关元素
            const videoSelectors = [
                'video',
                'a[href*="video"]',
                'a[href*="watch"]',
                'a[href*="v="]',
                '[class*="video"]',
                '[data-video]',
                '.video-item',
                '.video-card',
                '.video-link'
            ];

            for (const selector of videoSelectors) {
                const videoElements = document.querySelectorAll(selector);
                videoElements.forEach(el => {
                    if (isElementVisible(el) && !elements.some(item => item.element === el)) {
                        elements.push({
                            element: el,
                            text: getElementText(el) || '视频',
                            url: el.href || null,
                            type: 'video'
                        });
                    }
                });
            }
            break;

        case 'image':
        case '图片':
            const imageSelectors = ['img', 'a[href*=".jpg"]', 'a[href*=".png"]', '[class*="image"]'];
            for (const selector of imageSelectors) {
                const imgElements = document.querySelectorAll(selector);
                imgElements.forEach(el => {
                    if (isElementVisible(el) && !elements.some(item => item.element === el)) {
                        elements.push({
                            element: el,
                            text: el.alt || el.title || getElementText(el) || '图片',
                            url: el.src || el.href || null,
                            type: 'image'
                        });
                    }
                });
            }
            break;

        case 'link':
        case '链接':
            const linkElements = document.querySelectorAll('a[href]');
            linkElements.forEach(el => {
                if (isElementVisible(el)) {
                    elements.push({
                        element: el,
                        text: getElementText(el) || '链接',
                        url: el.href,
                        type: 'link'
                    });
                }
            });
            break;

        case 'button':
        case '按钮':
            const buttonElements = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            buttonElements.forEach(el => {
                if (isElementVisible(el)) {
                    elements.push({
                        element: el,
                        text: getElementText(el) || '按钮',
                        url: null,
                        type: 'button'
                    });
                }
            });
            break;

        case 'article':
        case '文章':
            const articleSelectors = ['article', '.article', '.post', '[class*="article"]', '[class*="post"]'];
            for (const selector of articleSelectors) {
                const articleElements = document.querySelectorAll(selector);
                articleElements.forEach(el => {
                    if (isElementVisible(el) && !elements.some(item => item.element === el)) {
                        elements.push({
                            element: el,
                            text: getElementText(el).substring(0, 50) + '...' || '文章',
                            url: el.querySelector('a')?.href || null,
                            type: 'article'
                        });
                    }
                });
            }
            break;

        default:
            // 通用选择器
            const genericElements = document.querySelectorAll(`[class*="${type}"], [data-${type}], .${type}`);
            genericElements.forEach(el => {
                if (isElementVisible(el)) {
                    elements.push({
                        element: el,
                        text: getElementText(el) || type,
                        url: el.href || null,
                        type: type
                    });
                }
            });
    }

    // 按页面位置排序（从上到下，从左到右）
    elements.sort((a, b) => {
        const rectA = a.element.getBoundingClientRect();
        const rectB = b.element.getBoundingClientRect();

        // 首先按Y坐标排序
        if (Math.abs(rectA.top - rectB.top) > 10) {
            return rectA.top - rectB.top;
        }

        // Y坐标相近时按X坐标排序
        return rectA.left - rectB.left;
    });

    return elements;
}

function sendPageInfoUpdate() {
    if (!isContentScriptActive) return;

    const currentPageContent = pageToMarkdown();
    if (currentPageContent && currentPageContent !== lastPageContent) {
        lastPageContent = currentPageContent;
        sendMessageWithRetry({
            type: 'PAGE_INFO_UPDATE',
            data: { markdown: currentPageContent }
        });
    }
}

// 带重试机制的消息发送
function sendMessageWithRetry(message, retryCount = 0) {
    if (!isContentScriptActive || retryCount >= maxRetryCount) {
        return;
    }

    try {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                const error = chrome.runtime.lastError.message;
                console.log(`Message send failed (attempt ${retryCount + 1}):`, error);

                // 如果是上下文失效错误，停止重试
                if (error.includes("Extension context invalidated") ||
                    error.includes("Could not establish connection")) {
                    console.log("Content script context invalidated, stopping operations");
                    isContentScriptActive = false;
                    return;
                }

                // 其他错误，延迟重试
                setTimeout(() => {
                    sendMessageWithRetry(message, retryCount + 1);
                }, 1000 * (retryCount + 1)); // 递增延迟
            }
        });
    } catch (error) {
        console.error("Failed to send message:", error);
        isContentScriptActive = false;
    }
}

// 增强的元素查找函数
function findTargetElement(target) {
    // 1. 首先通过vcp-id查找
    let element = document.querySelector(`[vcp-id="${target}"]`);
    if (element && isElementVisible(element)) {
        return element;
    }

    // 2. 通过精确文本匹配查找
    const allInteractiveElements = document.querySelectorAll('[vcp-id]');
    for (const el of allInteractiveElements) {
        if (!isElementVisible(el)) continue;

        const elText = getElementText(el);
        if (elText === target) {
            return el;
        }
    }

    // 3. 通过模糊文本匹配查找（去除多余空格、换行）
    for (const el of allInteractiveElements) {
        if (!isElementVisible(el)) continue;

        const elText = getElementText(el);
        const normalizedElText = normalizeText(elText);
        const normalizedTarget = normalizeText(target);

        if (normalizedElText === normalizedTarget) {
            return el;
        }
    }

    // 4. 通过包含关系查找
    for (const el of allInteractiveElements) {
        if (!isElementVisible(el)) continue;

        const elText = getElementText(el);
        const normalizedElText = normalizeText(elText);
        const normalizedTarget = normalizeText(target);

        if (normalizedElText.includes(normalizedTarget) || normalizedTarget.includes(normalizedElText)) {
            return el;
        }
    }

    // 5. 通过属性查找（placeholder, name, id等）
    const targetLower = target.toLowerCase();
    for (const el of allInteractiveElements) {
        if (!isElementVisible(el)) continue;

        const attributes = [
            el.placeholder,
            el.name,
            el.id,
            el.getAttribute('aria-label'),
            el.getAttribute('title')
        ].filter(attr => attr);

        for (const attr of attributes) {
            if (normalizeText(attr).includes(normalizeText(target))) {
                return el;
            }
        }
    }

    // 6. 重新扫描页面，可能有新元素
    refreshPageElements();

    // 7. 再次尝试基本查找
    element = document.querySelector(`[vcp-id="${target}"]`);
    if (element && isElementVisible(element)) {
        return element;
    }

    return null;
}

// 检查元素是否可见
function isElementVisible(element) {
    if (!element) return false;

    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
        return false;
    }

    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
}

// 获取元素文本内容
function getElementText(element) {
    return (element.innerText || element.value || element.placeholder ||
            element.ariaLabel || element.title || '').trim();
}

// 标准化文本（去除多余空格、换行等）
function normalizeText(text) {
    if (!text) return '';
    return text.replace(/\s+/g, ' ').trim().toLowerCase();
}

// 刷新页面元素（重新分配vcp-id）
function refreshPageElements() {
    // 清除现有的vcp-id
    document.querySelectorAll('[vcp-id]').forEach(el => el.removeAttribute('vcp-id'));
    vcpIdCounter = 0;

    // 重新识别可交互元素
    const body = document.body;
    if (body) {
        processNodeForRefresh(body);
    }
}

// 为刷新处理节点
function processNodeForRefresh(node) {
    if (!node || node.nodeType !== Node.ELEMENT_NODE) return;

    if (isInteractive(node)) {
        formatInteractiveElement(node);
    }

    // 递归处理子节点
    for (const child of node.children) {
        processNodeForRefresh(child);
    }
}

// 获取可用元素列表（用于错误提示）
function getAvailableElements() {
    const elements = [];
    const allInteractiveElements = document.querySelectorAll('[vcp-id]');

    for (const el of allInteractiveElements) {
        if (!isElementVisible(el)) continue;

        const text = getElementText(el);
        const vcpId = el.getAttribute('vcp-id');

        if (text && text.length < 50) {
            elements.push(`"${text}" (${vcpId})`);
        }
    }

    return elements.slice(0, 10).join(', ') + (elements.length > 10 ? '...' : '');
}

// 带等待的元素查找
function findTargetElementWithWait(target, timeout = 3000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();

        function attemptFind() {
            const element = findTargetElement(target);
            if (element) {
                resolve(element);
                return;
            }

            if (Date.now() - startTime >= timeout) {
                resolve(null);
                return;
            }

            // 等待一段时间后重试
            setTimeout(attemptFind, 200);
        }

        attemptFind();
    });
}

// 执行命令
async function executeCommand(command, element, text, target) {
    try {
        switch (command) {
            case 'type':
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    // 先清空现有内容
                    element.value = '';
                    element.focus();

                    // 模拟真实输入
                    element.value = text;

                    // 触发输入事件
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));

                    return { status: 'success', message: `成功在 '${target}' 中输入文本。` };
                } else {
                    throw new Error(`'${target}' 不是一个输入框。`);
                }

            case 'click':
                // 滚动到元素可见位置
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // 等待滚动完成
                await new Promise(resolve => setTimeout(resolve, 300));

                // 确保元素仍然可见
                if (!isElementVisible(element)) {
                    throw new Error(`元素 '${target}' 滚动后仍不可见`);
                }

                // 聚焦元素
                try {
                    element.focus();
                } catch (e) {
                    // 某些元素可能无法聚焦，忽略错误
                }

                // 模拟真实点击事件序列
                const rect = element.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;

                // mousedown事件
                const mouseDownEvent = new MouseEvent('mousedown', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: centerX,
                    clientY: centerY
                });
                element.dispatchEvent(mouseDownEvent);

                // 短暂延迟
                await new Promise(resolve => setTimeout(resolve, 50));

                // mouseup事件
                const mouseUpEvent = new MouseEvent('mouseup', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: centerX,
                    clientY: centerY
                });
                element.dispatchEvent(mouseUpEvent);

                // click事件
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    clientX: centerX,
                    clientY: centerY
                });
                element.dispatchEvent(clickEvent);

                return { status: 'success', message: `成功点击了 '${target}'。` };

            case 'scroll':
                handleScrollCommand({ direction: text, target: target }, {});
                return { status: 'success', message: `成功执行滚动操作。` };

            case 'get_links':
                return getAllLinks();

            case 'debug_page':
                return getPageDebugInfo();

            case 'search':
                return await performSearch(text);

            case 'select_by_position':
                return await selectByPosition(commandData);

            case 'click_first_video':
                return await selectByPosition({ type: 'video', position: 'first', action: 'click' });

            case 'click_at_coordinates':
                return clickAtCoordinates(commandData.x, commandData.y);

            case 'find_search_boxes':
                return findAllSearchBoxes();

            default:
                throw new Error(`不支持的命令: ${command}`);
        }
    } catch (error) {
        return { status: 'error', error: error.message };
    }
}

// 发送命令结果
function sendCommandResult(requestId, sourceClientId, result) {
    sendMessageWithRetry({
        type: 'COMMAND_RESULT',
        data: {
            requestId,
            sourceClientId,
            ...result
        }
    });
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'CLEAR_STATE') {
        lastPageContent = '';
    } else if (request.type === 'PREPARE_CAPTURE') {
        // 准备截图：在可交互元素上添加视觉标注
        prepareCaptureAnnotations(request.data.include_elements);
        sendResponse({ status: 'ready' });
    } else if (request.type === 'CLEANUP_CAPTURE') {
        // 清理截图标注
        cleanupCaptureAnnotations();
        sendResponse({ status: 'cleaned' });
    } else if (request.type === 'REQUEST_PAGE_INFO_UPDATE') {
        sendPageInfoUpdate();
    } else if (request.type === 'EXECUTE_COMMAND') {
        const { command, target, text, requestId, sourceClientId } = request.data;
        let result = {};

        // 使用智能等待机制查找元素
        findTargetElementWithWait(target, 3000).then(element => {
            if (!element) {
                const availableElements = getAvailableElements();
                result = {
                    status: 'error',
                    error: `未能在页面上找到目标为 '${target}' 的元素。\n可用元素: ${availableElements}`
                };
                sendCommandResult(requestId, sourceClientId, result);
                return;
            }

            executeCommand(command, element, text, target).then(cmdResult => {
                sendCommandResult(requestId, sourceClientId, cmdResult);
                setTimeout(sendPageInfoUpdate, 500);
            }).catch(error => {
                sendCommandResult(requestId, sourceClientId, {
                    status: 'error',
                    error: error.message
                });
            });
        }).catch(error => {
            sendCommandResult(requestId, sourceClientId, {
                status: 'error',
                error: error.message
            });
        });

        return; // 异步处理，直接返回
    }
});

const debouncedSendPageInfoUpdate = debounce(sendPageInfoUpdate, 500); // 降低延迟，提高响应速度

const observer = new MutationObserver((mutations) => {
    debouncedSendPageInfoUpdate();
});
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    characterData: true
});

document.addEventListener('click', debouncedSendPageInfoUpdate);
document.addEventListener('focusin', debouncedSendPageInfoUpdate);
document.addEventListener('scroll', debouncedSendPageInfoUpdate, true); // 监听滚动事件

// 页面加载完成时发送更新
if (document.readyState === 'loading') {
    window.addEventListener('load', sendPageInfoUpdate);
} else {
    // 如果页面已经加载完成，立即发送更新
    setTimeout(sendPageInfoUpdate, 100);
}

// 页面可见性变化时发送更新
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible' && isContentScriptActive) {
        setTimeout(sendPageInfoUpdate, 100); // 短暂延迟确保页面完全可见
    }
});

// 定期发送更新，但只在页面可见时
setInterval(() => {
    if (document.visibilityState === 'visible' && isContentScriptActive) {
        sendPageInfoUpdate();
    }
}, 5000);

// 截图标注相关变量
let captureAnnotations = [];

// 准备截图标注
function prepareCaptureAnnotations(includeElements = true) {
    if (!includeElements) return;

    // 清理之前的标注
    cleanupCaptureAnnotations();

    // 为所有可交互元素添加视觉标注
    const interactiveElements = document.querySelectorAll('[vcp-id]');

    interactiveElements.forEach((element, index) => {
        if (!isElementVisible(element)) return;

        const rect = element.getBoundingClientRect();
        const vcpId = element.getAttribute('vcp-id');

        // 创建标注覆盖层
        const annotation = document.createElement('div');
        annotation.className = 'vcp-capture-annotation';
        annotation.style.cssText = `
            position: fixed;
            left: ${rect.left}px;
            top: ${rect.top}px;
            width: ${rect.width}px;
            height: ${rect.height}px;
            border: 2px solid #ff4444;
            background: rgba(255, 68, 68, 0.1);
            pointer-events: none;
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #ff4444;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            padding: 2px;
            box-sizing: border-box;
        `;

        // 添加标注文本
        const label = document.createElement('span');
        label.style.cssText = `
            background: #ff4444;
            color: white;
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 10px;
            line-height: 1;
        `;
        label.textContent = vcpId.replace('vcp-id-', '');
        annotation.appendChild(label);

        document.body.appendChild(annotation);
        captureAnnotations.push(annotation);
    });
}

// 清理截图标注
function cleanupCaptureAnnotations() {
    captureAnnotations.forEach(annotation => {
        if (annotation.parentNode) {
            annotation.parentNode.removeChild(annotation);
        }
    });
    captureAnnotations = [];
}

// 根据坐标点击
function clickAtCoordinates(x, y) {
    try {
        // 将坐标转换为数字
        const clickX = parseInt(x);
        const clickY = parseInt(y);

        if (isNaN(clickX) || isNaN(clickY)) {
            return {
                status: 'error',
                error: '无效的坐标值'
            };
        }

        // 获取指定坐标处的元素
        const element = document.elementFromPoint(clickX, clickY);

        if (!element) {
            return {
                status: 'error',
                error: `坐标 (${clickX}, ${clickY}) 处没有找到元素`
            };
        }

        // 创建点击事件
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
            clientX: clickX,
            clientY: clickY
        });

        // 触发点击事件
        element.dispatchEvent(clickEvent);

        // 获取元素信息
        const elementInfo = {
            tagName: element.tagName.toLowerCase(),
            text: getElementText(element),
            className: element.className,
            id: element.id,
            href: element.href || null
        };

        return {
            status: 'success',
            message: `成功点击坐标 (${clickX}, ${clickY}) 处的元素`,
            data: {
                coordinates: { x: clickX, y: clickY },
                element: elementInfo
            }
        };

    } catch (error) {
        return {
            status: 'error',
            error: `坐标点击失败: ${error.message}`
        };
    }
}

// 查找所有可能的搜索框
function findAllSearchBoxes() {
    try {
        const searchBoxes = [];

        // 所有可能的搜索框选择器
        const selectors = [
            'input[type="search"]',
            'input[name*="search"]',
            'input[placeholder*="搜索"]',
            'input[placeholder*="search"]',
            'input[id*="search"]',
            'input[class*="search"]',
            '[role="search"] input',
            '.search-box input',
            '.search-input',
            '#search',
            '.search',
            'input[type="text"]',
            'input:not([type])'
        ];

        const foundElements = new Set();

        for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (foundElements.has(element)) return;
                foundElements.add(element);

                const info = {
                    selector: selector,
                    tagName: element.tagName.toLowerCase(),
                    type: element.type || 'text',
                    id: element.id || null,
                    name: element.name || null,
                    className: element.className || null,
                    placeholder: element.placeholder || null,
                    value: element.value || null,
                    visible: isElementVisible(element),
                    position: (() => {
                        try {
                            const rect = element.getBoundingClientRect();
                            return {
                                x: Math.round(rect.x),
                                y: Math.round(rect.y),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height)
                            };
                        } catch (e) {
                            return null;
                        }
                    })(),
                    parentForm: element.closest('form') ? true : false,
                    ariaLabel: element.getAttribute('aria-label') || null,
                    title: element.title || null
                };

                // 计算搜索相关性得分
                let relevanceScore = 0;
                if (element.type === 'search') relevanceScore += 10;
                if (element.name && element.name.toLowerCase().includes('search')) relevanceScore += 8;
                if (element.id && element.id.toLowerCase().includes('search')) relevanceScore += 8;
                if (element.placeholder && element.placeholder.toLowerCase().includes('search')) relevanceScore += 6;
                if (element.placeholder && element.placeholder.includes('搜索')) relevanceScore += 6;
                if (element.className && element.className.toLowerCase().includes('search')) relevanceScore += 4;
                if (element.closest('[role="search"]')) relevanceScore += 5;
                if (element.closest('form')) relevanceScore += 2;
                if (isElementVisible(element)) relevanceScore += 3;

                info.relevanceScore = relevanceScore;
                searchBoxes.push(info);
            });
        }

        // 按相关性得分排序
        searchBoxes.sort((a, b) => b.relevanceScore - a.relevanceScore);

        return {
            status: 'success',
            message: `找到 ${searchBoxes.length} 个可能的搜索框`,
            data: {
                count: searchBoxes.length,
                searchBoxes: searchBoxes,
                recommendation: searchBoxes.length > 0 ?
                    `推荐使用: ${searchBoxes[0].selector} (得分: ${searchBoxes[0].relevanceScore})` :
                    '未找到明显的搜索框'
            }
        };

    } catch (error) {
        return {
            status: 'error',
            error: `查找搜索框失败: ${error.message}`
        };
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}