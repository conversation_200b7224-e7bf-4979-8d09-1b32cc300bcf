<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多媒体缓存编辑器</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="image_cache_editor.css">
    <script>
        // 监听来自父窗口的主题变化消息
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'themeChange') {
                const theme = event.data.theme;
                document.documentElement.setAttribute('data-theme', theme);
                // 可以选择将主题保存到 localStorage，以便在独立打开时保持一致
                // localStorage.setItem('theme', theme);
            }
        });

        // 页面加载时，向父窗口请求当前主题
        window.addEventListener('DOMContentLoaded', () => {
            if (window.parent && window.parent !== window) {
                 window.parent.postMessage({ type: 'requestTheme' }, '*');
            } else {
                // 如果是独立打开，则应用本地存储的主题或默认主题
                const storedTheme = localStorage.getItem('theme');
                const prefersDarkScheme = window.matchMedia("(prefers-color-scheme: dark)");
                if (storedTheme) {
                    document.documentElement.setAttribute('data-theme', storedTheme);
                } else {
                    document.documentElement.setAttribute('data-theme', prefersDarkScheme.matches ? 'dark' : 'light');
                }
            }
        });
    </script>
</head>
<body>
<div class="container">
    <div class="controls">
        <button id="saveButton">保存更改到 multimodal_cache.json</button>
        <!--  按钮暂时不加入主题切换，因为iframe内切换可能与外部不同步，由外部统一控制 -->
    </div>

    <h2>多媒体缓存列表</h2>
    <div id="mediaList">
        <p>正在加载多媒体缓存数据...</p>
    </div>
</div>

<script>
    let mediaCacheData = {};
    const mediaListDiv = document.getElementById('mediaList');
    const saveButton = document.getElementById('saveButton');

    saveButton.addEventListener('click', handleSave);

    function guessMimeType(base64String) {
        if (base64String.startsWith('/9j/')) return 'image/jpeg';
        if (base64String.startsWith('iVBOR')) return 'image/png';
        if (base64String.startsWith('R0lGOD')) return 'image/gif';
        if (base64String.startsWith('UklGR')) return 'image/webp';
        // Add basic audio/video guesses if needed, though relying on entry.mimeType is better
        return 'application/octet-stream'; // Default for unknown
    }

    async function loadMediaCache() {
        try {
            const response = await fetch('/admin_api/multimodal-cache'); // UPDATED API ENDPOINT
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            mediaCacheData = await response.json();
            renderMediaList();
        } catch (error) {
            console.error("加载多媒体缓存失败:", error);
            mediaListDiv.innerHTML = '<p style="color: red;">加载多媒体缓存失败。</p>';
        }
    }

    async function handleSave() {
        if (Object.keys(mediaCacheData).length === 0) {
            alert('没有数据可保存。');
            return;
        }

        // Update mediaCacheData with current textarea values
        const entries = mediaListDiv.getElementsByClassName('media-entry');
        for (let i = 0; i < entries.length; i++) {
            const entryDiv = entries[i];
            const base64Key = entryDiv.dataset.base64Key;
            const textarea = entryDiv.querySelector('textarea');
            if (mediaCacheData[base64Key]) {
                mediaCacheData[base64Key].description = textarea.value;
            }
        }

        try {
            const response = await fetch('/admin_api/multimodal-cache', { // UPDATED API ENDPOINT
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ data: mediaCacheData }) // Send the data object
            });

            const result = await response.json();

            if (response.ok) {
                alert(result.message || '多媒体缓存文件已成功保存。');
            } else {
                alert('保存失败: ' + (result.error || '未知错误'));
                console.error("保存多媒体缓存失败:", result);
            }
        } catch (error) {
            alert('保存多媒体缓存时发生错误: ' + error.message);
            console.error("保存错误:", error);
        }
    }

    async function handleReidentify(base64Key, entryDiv) {
        const reidentifyBtn = entryDiv.querySelector('.reidentify-btn');
        const descriptionTextarea = entryDiv.querySelector('textarea');
        const timestampElement = entryDiv.querySelector('h3'); // Assuming h3 contains the timestamp
        const originalBtnText = reidentifyBtn.innerHTML;
        const originalBtnTitle = reidentifyBtn.title;

        reidentifyBtn.disabled = true;
        reidentifyBtn.innerHTML = '...'; // Loading indicator
        reidentifyBtn.title = '正在重新识别...';
        reidentifyBtn.style.opacity = '0.6'; // Dim the button
        reidentifyBtn.style.cursor = 'wait';

        // Optional: Add a temporary status message
        let statusSpan = entryDiv.querySelector('.reidentify-status');
        if (!statusSpan) {
            statusSpan = document.createElement('span');
            statusSpan.className = 'reidentify-status';
            statusSpan.style.fontSize = '0.8em';
            statusSpan.style.marginLeft = '10px';
            statusSpan.style.color = '#7aa1ff'; // Info color
            descriptionTextarea.parentNode.insertBefore(statusSpan, descriptionTextarea);
        }
        statusSpan.textContent = '正在重新识别...';
        statusSpan.style.color = '#7aa1ff';

        try {
            // Call the new backend API endpoint
            const response = await fetch('/admin_api/multimodal-cache/reidentify', { // UPDATED API ENDPOINT
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ base64Key: base64Key })
            });

            const result = await response.json();

            if (response.ok) {
                // Update description and timestamp
                descriptionTextarea.value = result.newDescription || '';
                timestampElement.textContent = `时间戳: ${result.newTimestamp || 'N/A'}`;

                // Update the mediaCacheData in memory as well
                if (mediaCacheData[base64Key]) {
                    mediaCacheData[base64Key].description = result.newDescription || '';
                    mediaCacheData[base64Key].timestamp = result.newTimestamp || mediaCacheData[base64Key].timestamp; // Keep old timestamp if new one is missing
                }


                statusSpan.textContent = '重新识别成功!';
                statusSpan.style.color = '#2ecc71'; // Success color
                console.log(`Reidentified ${base64Key.substring(0, 20)}...:`, result.newDescription);

            } else {
                statusSpan.textContent = '重新识别失败: ' + (result.error || '未知错误');
                statusSpan.style.color = '#e74c3c'; // Error color
                console.error("重新识别失败:", result);
            }
        } catch (error) {
            statusSpan.textContent = '重新识别时发生错误: ' + error.message;
            statusSpan.style.color = '#e74c3c'; // Error color
            console.error("重新识别错误:", error);
        } finally {
            // Restore button state
            reidentifyBtn.disabled = false;
            reidentifyBtn.innerHTML = originalBtnText;
            reidentifyBtn.title = originalBtnTitle;
            reidentifyBtn.style.opacity = '1';
            reidentifyBtn.style.cursor = 'pointer';

            // Remove status message after a delay
            setTimeout(() => {
                if (statusSpan && statusSpan.parentNode) {
                     statusSpan.parentNode.removeChild(statusSpan);
                }
            }, 5000); // Remove after 5 seconds
        }
    }

    function renderMediaList() {
        mediaListDiv.innerHTML = ''; // 清空现有列表

        if (Object.keys(mediaCacheData).length === 0) {
            mediaListDiv.innerHTML = '<p>缓存文件为空。</p>';
            return;
        }

        const mediaKeys = Object.keys(mediaCacheData).reverse(); // 获取键并反转顺序

        for (const base64Key of mediaKeys) {
            if (mediaCacheData.hasOwnProperty(base64Key)) {
                const entry = mediaCacheData[base64Key];
                
                const entryDiv = document.createElement('div');
                entryDiv.className = 'media-entry'; // Use a more generic class name
                entryDiv.dataset.base64Key = base64Key; // 存储 key 用于保存

                const reidentifyBtn = document.createElement('span');
                reidentifyBtn.className = 'reidentify-btn';
                reidentifyBtn.innerHTML = '↻';
                reidentifyBtn.title = '重新识别媒体描述';
                reidentifyBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    handleReidentify(base64Key, entryDiv);
                });
                entryDiv.appendChild(reidentifyBtn);

                const deleteBtn = document.createElement('span');
                deleteBtn.className = 'delete-btn';
                deleteBtn.innerHTML = '&times;';
                deleteBtn.title = '删除此条目';
                deleteBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    if (!confirm(`确定要删除这个媒体条目吗？\nKey (部分): ${base64Key.substring(0,20)}...`)) {
                        return;
                    }
                    if (mediaCacheData.hasOwnProperty(base64Key)) {
                        delete mediaCacheData[base64Key];
                    }
                    entryDiv.remove();
                    if (Object.keys(mediaCacheData).length === 0) {
                        mediaListDiv.innerHTML = '<p>缓存文件为空或所有条目已删除。</p>';
                    }
                });
                entryDiv.appendChild(deleteBtn);

                const title = document.createElement('h3');
                title.textContent = `时间戳: ${entry.timestamp || 'N/A'}`;
                entryDiv.appendChild(title);

                // --- DYNAMIC MEDIA ELEMENT CREATION ---
                const mimeType = entry.mimeType || guessMimeType(base64Key);
                let mediaElement;

                if (mimeType.includes('image/')) {
                    mediaElement = document.createElement('img');
                    mediaElement.alt = '图像预览';
                    mediaElement.addEventListener('click', function() {
                        const modal = document.getElementById('mediaModal');
                        const modalContent = document.getElementById('modalContent');
                        if (modal && modalContent) {
                            modalContent.innerHTML = ''; // Clear previous content
                            const clone = this.cloneNode();
                            clone.style.maxWidth = '90%';
                            clone.style.maxHeight = '90vh';
                            modalContent.appendChild(clone);
                            modal.style.display = "block";
                            document.body.style.overflow = 'hidden';
                        }
                    });
                } else if (mimeType.includes('audio/')) {
                    mediaElement = document.createElement('audio');
                    mediaElement.controls = true;
                } else if (mimeType.includes('video/')) {
                    mediaElement = document.createElement('video');
                    mediaElement.controls = true;
                } else {
                    mediaElement = document.createElement('div');
                    mediaElement.className = 'unsupported-media';
                    mediaElement.innerHTML = `<p>不支持的媒体类型</p><span>${mimeType}</span>`;
                }
                
                if (mediaElement.tagName !== 'DIV') {
                    mediaElement.src = `${mimeType}base64,${base64Key}`;
                }

                mediaElement.onerror = function() {
                    this.alt = '预览失败';
                    this.style.display = 'none';
                    const errorMsg = document.createElement('p');
                    errorMsg.textContent = '媒体预览失败，可能是不支持的格式或损坏的数据。';
                    errorMsg.style.color = 'red';
                    entryDiv.insertBefore(errorMsg, this.nextSibling);
                };
                entryDiv.appendChild(mediaElement);
                
                const descriptionLabel = document.createElement('label');
                descriptionLabel.textContent = '媒体描述 (可编辑):';
                entryDiv.appendChild(descriptionLabel);

                const descriptionTextarea = document.createElement('textarea');
                descriptionTextarea.value = entry.description || '';
                entryDiv.appendChild(descriptionTextarea);

                const keyInfo = document.createElement('div');
                keyInfo.className = 'base64-key';
                keyInfo.textContent = `Base64 Key (部分): ${base64Key.substring(0, 30)}...`;
                entryDiv.appendChild(keyInfo);

                mediaListDiv.appendChild(entryDiv);
            }
        }
    }

    // 3. 允许图片点击放大预览 - Modal handling JavaScript
    window.addEventListener('DOMContentLoaded', () => {
        const modal = document.getElementById('mediaModal');
        const modalCloseButton = document.getElementById('modalCloseButton');

        function closeModal() {
            modal.style.display = "none";
            document.body.style.overflow = 'auto';
            // Stop any playing media
            const mediaContent = modal.querySelector('video, audio');
            if (mediaContent) {
                mediaContent.pause();
                mediaContent.src = ''; // Detach source
            }
        }

        if (modal && modalCloseButton) {
            modalCloseButton.onclick = closeModal;

            modal.onclick = function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            }

            document.addEventListener('keydown', function(event) {
                if (event.key === "Escape" && modal.style.display === "block") {
                    closeModal();
                }
            });
        } else {
            console.error('Modal or modal close button not found on DOMContentLoaded. Check IDs: mediaModal, modalCloseButton');
        }

        // Load data when the page loads
        loadMediaCache();
    });
</script>

<!-- Modal for Media Preview -->
<div id="mediaModal" class="modal">
    <span class="modal-close" id="modalCloseButton">&times;</span>
    <div class="modal-content" id="modalContent"></div>
</div>
</body>
</html>
