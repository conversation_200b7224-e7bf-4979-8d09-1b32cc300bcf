services:
  app:
    build: .
    container_name: vcptoolbox
    ports:
      - "6005:6005"
    environment:
      TZ: Asia/Shanghai        # 环境变量
    volumes:
      #1 将本地的 config.env 映射到容器中
      #1- ./config.env:/usr/src/app/config.env
      # 将插件的配置文件和数据目录映射进去
      # 注意: 如果插件的 config.env 文件不存在于宿主机，Docker 卷映射可能会创建目录而不是文件。
      # 确保这些文件在启动容器前在宿主机上存在，或者调整为映射整个插件目录（如果插件设计允许）。
      # 为了简单起见，这里假设目标是映射特定文件和已知数据目录。
#######
#
#为了docker desktop用户不用频繁构建镜像，依然推荐采取全挂载的方式
#
#######
      #0 插件特定配置和数据示例 (根据 .dockerignore 和已知插件调整)
      #0- ./Plugin/WeatherReporter/city_cache.txt:/usr/src/app/Plugin/WeatherReporter/city_cache.txt
      #0- ./Plugin/VCPLog/log:/usr/src/app/Plugin/VCPLog/log
      #0 - ./Plugin/ImageProcessor/cache:/usr/src/app/Plugin/ImageProcessor/cache # 假设的缓存目录
      #0- ./dailynote:/usr/src/app/dailynote
      #0- ./image:/usr/src/app/image # 映射整个 image 目录，包括 fluxgen 等子目录

      - .:/usr/src/app
      # 保持 node_modules 独立于容器，避免本地开发环境的 node_modules 覆盖容器内的。添加pydeps兼容科学计算器依赖。
      - /usr/src/app/pydeps
      - /usr/src/app/node_modules
      
    command: node server.js
    restart: unless-stopped
