# OpenAI 兼容图像生成插件

这是一个为 VCP (Variable & Command Protocol) 系统开发的图像生成插件，支持使用兼容 OpenAI API 的服务来生成图像。

## ✨ 功能特性

- 🎨 **DALL-E 风格图像生成** - 支持高质量的 AI 图像生成
- 🔧 **API 兼容性** - 兼容 OpenAI API 格式的第三方服务
- 📐 **多种尺寸支持** - 支持正方形、横屏、竖屏等多种尺寸
- 🎭 **风格控制** - 支持鲜艳(vivid)和自然(natural)两种风格
- 💾 **本地保存** - 可选的本地图像保存功能
- 🔄 **智能重试** - 自动重试机制，提高成功率
- 🐛 **调试支持** - 详细的调试日志和错误信息
- 🌐 **中英文支持** - 支持中文和英文提示词
- 📊 **批量生成** - 支持一次生成多张图片

## 🚀 快速安装

### 方法一：自动安装（推荐）

在插件目录下运行安装向导：

```bash
cd Plugin/OpenAIImageGen
npm install
node install.js
```

安装向导将引导您完成所有配置。

### 方法二：手动安装

1. **安装依赖**：
   ```bash
   cd Plugin/OpenAIImageGen
   npm install
   ```

2. **配置插件**：
   编辑项目根目录的 `config.env` 文件，添加以下配置：
   ```env
   # OpenAI 兼容图像生成插件配置
   OPENAI_API_KEY=your_api_key_here
   OPENAI_API_URL=https://your-api-endpoint.com
   OPENAI_IMAGE_MODEL=dall-e-3
   DEFAULT_IMAGE_SIZE=1024x1024
   DEFAULT_IMAGE_QUALITY=standard
   DEFAULT_IMAGE_STYLE=vivid
   MAX_RETRIES=3
   SAVE_IMAGES_LOCALLY=true
   LOCAL_SAVE_PATH=image/openai_generated
   ```

3. **启用插件**：
   插件已自动添加到工具列表中，重启 VCP 服务即可使用。

## 使用方法

### 基本用法

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」OpenAIImageGen「末」,
prompt:「始」一只可爱的橘猫坐在樱花树下「末」
<<<[END_TOOL_REQUEST]>>>
```

### 高级用法

```
<<<[TOOL_REQUEST]>>>
tool_name:「始」OpenAIImageGen「末」,
prompt:「始」未来科技城市的夜景，霓虹灯闪烁，赛博朋克风格「末」,
size:「始」1792x1024「末」,
quality:「始」hd「末」,
style:「始」vivid「末」,
n:「始」2「末」
<<<[END_TOOL_REQUEST]>>>
```

## 参数说明

| 参数 | 类型 | 必需 | 说明 | 可选值 |
|------|------|------|------|--------|
| prompt | string | 是 | 图像生成提示词 | 任意文本 |
| size | string | 否 | 图像尺寸 | 1024x1024, 1792x1024, 1024x1792 |
| quality | string | 否 | 图像质量 | standard, hd |
| style | string | 否 | 图像风格 | vivid, natural |
| n | integer | 否 | 生成数量 | 1-4 |

## 配置选项

### 必需配置

- `OPENAI_API_KEY`: OpenAI API 密钥
- `OPENAI_API_URL`: API 基础URL

### 可选配置

- `OPENAI_IMAGE_MODEL`: 图像模型名称（默认：dall-e-3）
- `DEFAULT_IMAGE_SIZE`: 默认图像尺寸（默认：1024x1024）
- `DEFAULT_IMAGE_QUALITY`: 默认图像质量（默认：standard）
- `DEFAULT_IMAGE_STYLE`: 默认图像风格（默认：vivid）
- `MAX_RETRIES`: 最大重试次数（默认：3）
- `SAVE_IMAGES_LOCALLY`: 是否本地保存（默认：true）
- `LOCAL_SAVE_PATH`: 本地保存路径（默认：image/openai_generated）

## 兼容的 API 服务

此插件兼容以下类型的 API 服务：

1. **OpenAI 官方 API**
2. **兼容 OpenAI 格式的第三方服务**，如：
   - Azure OpenAI Service
   - 各种 OpenAI 代理服务
   - 自建的兼容服务

## 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查 `OPENAI_API_KEY` 是否正确
   - 确认 API 密钥有图像生成权限

2. **API URL 错误**
   - 确保 `OPENAI_API_URL` 格式正确
   - 不要在 URL 末尾包含 `/v1/images/generations`

3. **网络连接问题**
   - 检查网络连接
   - 确认 API 服务可访问

4. **图像保存失败**
   - 检查 `LOCAL_SAVE_PATH` 目录权限
   - 确保有足够的磁盘空间

### 调试模式

启用调试模式以获取详细日志：

```env
DebugMode=true
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的图像生成功能
- 支持多种参数配置
- 支持本地图像保存
- 支持自动重试机制

## 许可证

MIT License
