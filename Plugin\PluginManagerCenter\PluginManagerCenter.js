const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

class PluginManagerCenter {
    constructor() {
        this.config = {};
        this.debugMode = false;
        this.pluginDir = path.join(__dirname, '..');
        this.statsFile = path.join(__dirname, 'plugin_stats.json');
        this.configFile = path.join(__dirname, 'plugin_configs.json');
        this.disabledPluginsFile = path.join(__dirname, 'disabled_plugins.json');
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[PluginManagerCenter] ${message}`);
        }
    }

    async loadConfig() {
        try {
            // 从环境变量加载配置
            this.config = {
                ENABLE_PERFORMANCE_MONITORING: process.env.ENABLE_PERFORMANCE_MONITORING !== 'false',
                AUTO_CHECK_DEPENDENCIES: process.env.AUTO_CHECK_DEPENDENCIES !== 'false',
                STATS_RETENTION_DAYS: parseInt(process.env.STATS_RETENTION_DAYS) || 30
            };
            this.debugMode = process.env.DebugMode === 'true';
            this.log('Configuration loaded');
        } catch (error) {
            this.log(`Failed to load config: ${error.message}`);
        }
    }

    async getAllPlugins() {
        try {
            const pluginDirs = await fs.readdir(this.pluginDir, { withFileTypes: true });
            const plugins = [];

            for (const dir of pluginDirs) {
                if (dir.isDirectory() && dir.name !== 'PluginManagerCenter') {
                    try {
                        const manifestPath = path.join(this.pluginDir, dir.name, 'plugin-manifest.json');
                        const manifestContent = await fs.readFile(manifestPath, 'utf8');
                        const manifest = JSON.parse(manifestContent);
                        
                        plugins.push({
                            name: manifest.name,
                            displayName: manifest.displayName,
                            version: manifest.version,
                            description: manifest.description,
                            pluginType: manifest.pluginType,
                            author: manifest.author,
                            path: path.join(this.pluginDir, dir.name),
                            manifest: manifest
                        });
                    } catch (error) {
                        this.log(`Failed to load plugin ${dir.name}: ${error.message}`);
                        plugins.push({
                            name: dir.name,
                            displayName: dir.name,
                            version: 'unknown',
                            description: 'Failed to load manifest',
                            pluginType: 'unknown',
                            author: 'unknown',
                            path: path.join(this.pluginDir, dir.name),
                            error: error.message
                        });
                    }
                }
            }

            return plugins;
        } catch (error) {
            throw new Error(`Failed to scan plugins: ${error.message}`);
        }
    }

    async getDisabledPlugins() {
        try {
            const content = await fs.readFile(this.disabledPluginsFile, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            return [];
        }
    }

    async setPluginDisabled(pluginName, disabled) {
        try {
            let disabledPlugins = await this.getDisabledPlugins();
            
            if (disabled) {
                if (!disabledPlugins.includes(pluginName)) {
                    disabledPlugins.push(pluginName);
                }
            } else {
                disabledPlugins = disabledPlugins.filter(name => name !== pluginName);
            }

            await fs.writeFile(this.disabledPluginsFile, JSON.stringify(disabledPlugins, null, 2));
            return true;
        } catch (error) {
            throw new Error(`Failed to update plugin status: ${error.message}`);
        }
    }

    async getPluginStats(pluginName = null, timeRange = '7d') {
        try {
            const content = await fs.readFile(this.statsFile, 'utf8');
            const allStats = JSON.parse(content);
            
            const days = parseInt(timeRange.replace('d', ''));
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);

            if (pluginName) {
                return allStats[pluginName] || { calls: 0, totalTime: 0, errors: 0, lastCall: null };
            }

            return allStats;
        } catch (error) {
            return pluginName ? { calls: 0, totalTime: 0, errors: 0, lastCall: null } : {};
        }
    }

    async checkPluginDependencies(pluginName = null) {
        try {
            this.log('Starting checkPluginDependencies...');
            const plugins = await this.getAllPlugins();
            const results = {};

            // 简化版本：只检查基本信息，避免复杂的依赖检查导致问题
            for (const plugin of plugins) {
                if (pluginName && plugin.name !== pluginName) continue;

                const depResult = {
                    satisfied: true,
                    missing: [],
                    errors: [],
                    checked: true
                };

                // 基本检查：如果插件有错误状态，标记为不满足
                if (plugin.error) {
                    depResult.satisfied = false;
                    depResult.errors.push(plugin.error);
                }

                // 简化的依赖检查
                if (plugin.manifest && plugin.manifest.dependencies) {
                    const deps = plugin.manifest.dependencies;

                    // 检查Node.js依赖（简化版）
                    if (deps.nodejs) {
                        const nodeVersion = process.version;
                        depResult.nodeVersion = nodeVersion;
                        depResult.requiredNode = deps.nodejs;
                    }

                    // 记录Python依赖要求（不实际检查以避免阻塞）
                    if (deps.python) {
                        depResult.requiredPython = deps.python;
                    }

                    // 记录库依赖（不实际检查以避免阻塞）
                    if (deps.libraries) {
                        depResult.requiredLibraries = deps.libraries;
                    }
                }

                results[plugin.name] = depResult;
            }

            this.log(`Dependency check completed for ${Object.keys(results).length} plugins`);

            if (pluginName) {
                if (!results[pluginName]) {
                    throw new Error(`Plugin "${pluginName}" not found`);
                }
                return { [pluginName]: results[pluginName] };
            }

            return results;
        } catch (error) {
            this.log(`Error in checkPluginDependencies: ${error.message}`);
            throw error;
        }
    }

    async checkPythonVersion() {
        return new Promise((resolve, reject) => {
            const python = spawn('python', ['--version']);
            let output = '';

            python.stdout.on('data', (data) => {
                output += data.toString();
            });

            python.stderr.on('data', (data) => {
                output += data.toString();
            });

            python.on('close', (code) => {
                if (code === 0) {
                    const match = output.match(/Python (\d+\.\d+\.\d+)/);
                    if (match) {
                        resolve(match[1]);
                    } else {
                        reject(new Error('Could not parse Python version'));
                    }
                } else {
                    reject(new Error('Python not found'));
                }
            });
        });
    }

    async checkLibrary(libName, type) {
        return new Promise((resolve, reject) => {
            let command, args;

            if (type === 'python') {
                command = 'python';
                args = ['-c', `import ${libName}`];
            } else if (type === 'nodejs') {
                command = 'node';
                args = ['-e', `require('${libName}')`];
            } else {
                reject(new Error(`Unsupported plugin type: ${type}`));
                return;
            }

            const process = spawn(command, args);
            
            process.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`Library ${libName} not found`));
                }
            });
        });
    }

    versionSatisfies(current, required) {
        // 简单的版本比较，支持 >=x.x.x 格式
        const cleanRequired = required.replace(/[>=<]/g, '');
        const currentParts = current.replace('v', '').split('.').map(Number);
        const requiredParts = cleanRequired.split('.').map(Number);

        for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
            const curr = currentParts[i] || 0;
            const req = requiredParts[i] || 0;
            
            if (curr > req) return true;
            if (curr < req) return false;
        }
        
        return true;
    }

    async listPlugins(params) {
        const { filter = 'all', detailed = 'false' } = params;
        const plugins = await this.getAllPlugins();
        const disabledPlugins = await this.getDisabledPlugins();
        const stats = await this.getPluginStats();

        let filteredPlugins = plugins.map(plugin => {
            const isDisabled = disabledPlugins.includes(plugin.name);
            const pluginStats = stats[plugin.name] || { calls: 0, lastCall: null };

            return {
                ...plugin,
                status: plugin.error ? 'error' : (isDisabled ? 'disabled' : 'enabled'),
                isDisabled,
                stats: pluginStats
            };
        });

        // 应用过滤器
        if (filter !== 'all') {
            filteredPlugins = filteredPlugins.filter(plugin => {
                switch (filter) {
                    case 'enabled': return plugin.status === 'enabled';
                    case 'disabled': return plugin.status === 'disabled';
                    case 'error': return plugin.status === 'error';
                    default: return true;
                }
            });
        }

        const result = {
            total: plugins.length,
            filtered: filteredPlugins.length,
            plugins: detailed === 'true' ? filteredPlugins : filteredPlugins.map(p => ({
                name: p.name,
                displayName: p.displayName,
                version: p.version,
                status: p.status,
                lastCall: p.stats.lastCall,
                totalCalls: p.stats.calls
            }))
        };

        return {
            status: 'success',
            result: result,
            messageForAI: `找到 ${result.total} 个插件，其中 ${result.filtered} 个符合筛选条件 "${filter}"。`
        };
    }

    async getPluginStatus(params) {
        const { plugin_name } = params;
        if (!plugin_name) {
            throw new Error('plugin_name parameter is required');
        }

        const plugins = await this.getAllPlugins();
        const plugin = plugins.find(p => p.name === plugin_name);

        if (!plugin) {
            throw new Error(`Plugin "${plugin_name}" not found`);
        }

        const disabledPlugins = await this.getDisabledPlugins();
        const isDisabled = disabledPlugins.includes(plugin_name);
        const stats = await this.getPluginStats(plugin_name);
        const dependencies = await this.checkPluginDependencies(plugin_name);

        const result = {
            ...plugin,
            status: plugin.error ? 'error' : (isDisabled ? 'disabled' : 'enabled'),
            isDisabled,
            stats,
            dependencies: dependencies[plugin_name] || { satisfied: true, missing: [], errors: [] }
        };

        return {
            status: 'success',
            result: result,
            messageForAI: `插件 "${plugin.displayName}" 状态: ${result.status}，依赖满足: ${result.dependencies.satisfied ? '是' : '否'}。`
        };
    }

    async togglePlugin(params) {
        const { plugin_name, action } = params;
        if (!plugin_name || !action) {
            throw new Error('plugin_name and action parameters are required');
        }

        if (!['enable', 'disable'].includes(action)) {
            throw new Error('action must be "enable" or "disable"');
        }

        const plugins = await this.getAllPlugins();
        const plugin = plugins.find(p => p.name === plugin_name);

        if (!plugin) {
            throw new Error(`Plugin "${plugin_name}" not found`);
        }

        const shouldDisable = action === 'disable';
        await this.setPluginDisabled(plugin_name, shouldDisable);

        return {
            status: 'success',
            result: {
                plugin_name,
                action,
                new_status: shouldDisable ? 'disabled' : 'enabled'
            },
            messageForAI: `插件 "${plugin.displayName}" 已${shouldDisable ? '禁用' : '启用'}。`
        };
    }

    async getPluginStatsCommand(params) {
        const { plugin_name, time_range = '7d' } = params;
        const stats = await this.getPluginStats(plugin_name, time_range);

        if (plugin_name) {
            return {
                status: 'success',
                result: { [plugin_name]: stats },
                messageForAI: `插件 "${plugin_name}" 在过去 ${time_range} 的统计: 调用 ${stats.calls} 次，平均耗时 ${stats.totalTime > 0 ? Math.round(stats.totalTime / stats.calls) : 0}ms。`
            };
        } else {
            const totalCalls = Object.values(stats).reduce((sum, s) => sum + (s.calls || 0), 0);
            return {
                status: 'success',
                result: stats,
                messageForAI: `过去 ${time_range} 所有插件总计调用 ${totalCalls} 次。`
            };
        }
    }

    async checkDependenciesCommand(params) {
        try {
            this.log('Starting dependency check...');
            const { plugin_name, fix_missing = 'false' } = params;
            const dependencies = await this.checkPluginDependencies(plugin_name);

            let summary;
            if (plugin_name) {
                const dep = dependencies[plugin_name];
                if (!dep) {
                    throw new Error(`Plugin "${plugin_name}" not found`);
                }
                summary = `插件 "${plugin_name}" 依赖检查: ${dep.satisfied ? '全部满足' : '存在问题'}`;
                if (!dep.satisfied) {
                    summary += `，缺失: ${dep.missing.join(', ')}`;
                }
            } else {
                const total = Object.keys(dependencies).length;
                const satisfied = Object.values(dependencies).filter(d => d.satisfied).length;
                summary = `检查了 ${total} 个插件，${satisfied} 个依赖满足，${total - satisfied} 个存在问题。`;
            }

            this.log('Dependency check completed successfully');
            return {
                status: 'success',
                result: dependencies,
                messageForAI: summary
            };
        } catch (error) {
            this.log(`Dependency check failed: ${error.message}`);
            return {
                status: 'error',
                error: error.message,
                messageForAI: `依赖检查失败: ${error.message}`
            };
        }
    }

    async managePluginConfig(params) {
        const { plugin_name, action, config_key, config_value } = params;

        if (!plugin_name || !action) {
            throw new Error('plugin_name and action parameters are required');
        }

        // 这里可以实现配置管理逻辑
        // 由于VCP的配置主要在环境变量中，这里提供一个基础框架

        return {
            status: 'success',
            result: { message: 'Config management feature coming soon' },
            messageForAI: '配置管理功能正在开发中，敬请期待。'
        };
    }

    async processRequest(request) {
        try {
            await this.loadConfig();
            const { command, ...params } = request;

            this.log(`Processing command: ${command}`);
            this.log(`Request params: ${JSON.stringify(params)}`);

            switch (command) {
                case 'list_plugins':
                    this.log('Executing list_plugins');
                    return await this.listPlugins(params);
                case 'plugin_status':
                    this.log('Executing plugin_status');
                    return await this.getPluginStatus(params);
                case 'toggle_plugin':
                    this.log('Executing toggle_plugin');
                    return await this.togglePlugin(params);
                case 'plugin_stats':
                    this.log('Executing plugin_stats');
                    return await this.getPluginStatsCommand(params);
                case 'check_dependencies':
                    this.log('Executing check_dependencies');
                    return await this.checkDependenciesCommand(params);
                case 'plugin_config':
                    this.log('Executing plugin_config');
                    return await this.managePluginConfig(params);
                default:
                    throw new Error(`Unknown command: ${command}`);
            }
        } catch (error) {
            this.log(`Error in processRequest: ${error.message}`);
            return {
                status: 'error',
                error: error.message,
                messageForAI: `插件管理操作失败: ${error.message}`
            };
        }
    }
}

// 主程序入口
async function main() {
    try {
        const input = await new Promise((resolve) => {
            let data = '';
            process.stdin.on('data', chunk => data += chunk);
            process.stdin.on('end', () => resolve(data.trim()));
        });

        const request = JSON.parse(input);
        const manager = new PluginManagerCenter();
        const result = await manager.processRequest(request);

        console.log(JSON.stringify(result));
        process.exit(0);
    } catch (error) {
        console.log(JSON.stringify({
            status: 'error',
            error: error.message,
            messageForAI: `插件管理中心执行失败: ${error.message}`
        }));
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = PluginManagerCenter;
