{"name": "Wan2.1VideoGen", "displayName": "视频生成器 (Wan2.1)", "version": "0.1.0", "description": "使用 Wan2.1 API 进行文本到视频或图像到视频的生成。", "pluginType": "asynchronous", "entryPoint": {"command": "python video_handler.py"}, "communication": {"protocol": "stdio", "timeout": 1800000}, "configSchema": {"SILICONFLOW_API_KEY": "string", "Text2VideoModelName": "string", "Image2VideoModelName": "string", "DebugMode": "boolean", "CALLBACK_BASE_URL": "string"}, "capabilities": {"invocationCommands": [{"command": "submit", "description": "提交一个新的视频生成任务。\n**严格按照以下格式和参数顺序调用，优先决定command参数，确定究竟是生成还是获取视频。其次是Mode参数，是t2v还是i2v。不要包含任何未列出的参数（例如 style, duration 等）。**\n\n**重要提示：视频生成需要很长时间（几分钟到几十分钟甚至更长），请在调用成功后告知用户任务已提交，并提醒他们需要耐心等待，稍后再使用 query 命令查询结果。**\n\n**通用必需参数 (必须按此顺序):**\n1. tool_name:「始」Wan2.1VideoGen「末」\n2. command:「始」submit「末」\n3. mode:「始」[模式]「末」 (必需, 值必须是 't2v' 或 'i2v')\n\n**模式特定参数 (紧跟通用参数之后):**\n*   **如果 mode 是 't2v' (文生视频):**\n    4. prompt:「始」[视频描述文本]「末」 (必需)\n    5. resolution:「始」[分辨率]「末」 (必需, 值必须是 '1280x720', '720x1280', 或 '960x960')\n    6. negative_prompt:「始」[负面提示词]「末」 (可选, 如果不需要请省略此行)\n*   **如果 mode 是 'i2v' (图生视频):**\n    4. image_url:「始」[图片链接]「末」 (必需)\n    5. prompt:「始」[指导性提示词]「末」 (可选, 如果不需要请省略此行)\n    6. negative_prompt:「始」[负面提示词]「末」 (可选, 如果不需要请省略此行)\n\n**禁止包含任何其他参数。** 插件会自动处理随机种子和 i2v 分辨率。\n\n**成功时返回 JSON:** { \"status\": \"success\", \"result\": { \"requestId\": \"...\" } }\n**失败时返回 JSON:** { \"status\": \"error\", \"error\": \"错误信息...\" }\n\n**调用示例 (t2v):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Wan2.1VideoGen「末」,\ncommand:「始」submit「末」,\nmode:「始」t2v「末」,\nprompt:「始」一只猫在太空漫步「末」,\nresolution:「始」1280x720「末」\n<<<[END_TOOL_REQUEST]>>>\n\n**调用示例 (i2v):**\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Wan2.1VideoGen「末」,\ncommand:「始」submit「末」,\nmode:「始」i2v「末」,\nimage_url:「始」http://example.com/cat.jpg「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "提交文生视频:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Wan2.1VideoGen「末」,\ncommand:「始」submit「末」,\nmode:「始」t2v「末」,\nprompt:「始」一只猫在太空漫步「末」,\nresolution:「始」1280x720「末」\n<<<[END_TOOL_REQUEST]>>>\n\n提交图生视频:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Wan2.1VideoGen「末」,\ncommand:「始」submit「末」,\nmode:「始」i2v「末」,\nimage_url:「始」http://example.com/cat.jpg「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "query", "description": "查询指定 request_id 的视频生成任务状态。\n**重要提示：如果状态是 'InProgress'，请告知用户仍在生成中，需要继续等待。如果状态是 'Succeed'，请将视频 URL 提供给用户。如果状态是 'Failed'，请告知用户生成失败及原因。**\n\n成功时返回 JSON: { \"status\": \"success\", \"result\": { \"status\": \"InProgress|Succeed|Failed\", \"results\": { \"videos\": [{ \"url\": \"...\" }] }, \"reason\": \"...\" } }\n(注意: result 字段直接来自 API 响应，可能包含更多信息)\n失败时返回 JSON: { \"status\": \"error\", \"error\": \"错误信息...\" }\n\n调用示例:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」Wan2.1VideoGen「末」,\ncommand:「始」query「末」,\nrequest_id:「始」your_request_id_here「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」Wan2.1VideoGen「末」,\ncommand:「始」query「末」,\nrequest_id:「始」your_request_id_here「末」\n<<<[END_TOOL_REQUEST]>>>"}]}, "webSocketPush": {"enabled": true, "messageType": "video_generation_status", "usePluginResultAsMessage": true, "targetClientType": "VCPLog"}}