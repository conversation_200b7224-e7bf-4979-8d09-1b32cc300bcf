{"name": "ChromeControl", "displayName": "Chrome 浏览器控制器", "version": "1.0.0", "description": "一个用于方便AI向Chrome浏览器发送操作指令（如点击、输入）的同步插件。", "pluginType": "synchronous", "entryPoint": {"command": "node ChromeControl.js"}, "communication": {"protocol": "stdio", "timeout": 15000}, "capabilities": {"invocationCommands": [{"command": "type", "description": "在指定的输入框中输入文本。\n- `command`: 固定为 `type`。\n- `target`: 输入框的标题或标识符 (例如, '搜索框', 'username')。\n- `text`: 要输入的文本内容。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」type「末」,\ntarget: 「始」搜索框「末」,\ntext: 「始」VCP Agent是什么「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」type「末」,\ntarget: 「始」搜索框「末」,\ntext: 「始」VCP Agent是什么「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "click", "description": "点击指定的按钮或链接。\n- `command`: 固定为 `click`。\n- `target`: 按钮或链接的标题或URL (例如, '登录', 'https://example.com/next')。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click「末」,\ntarget: 「始」登录「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click「末」,\ntarget: 「始」登录「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "open_url", "description": "在新的标签页中打开指定的URL。\n- `command`: 固定为 `open_url`。\n- `url`: 要打开的完整URL地址。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」open_url「末」,\nurl: 「始」https://www.google.com「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」open_url「末」,\nurl: 「始」https://www.google.com「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "scroll", "description": "滚动页面或指定元素。\n- `command`: 固定为 `scroll`。\n- `direction`: 滚动方向，支持: up(向上), down(向下), left(向左), right(向右), totop(滚动到顶部), tobottom(滚动到底部)。\n- `distance`: 可选，滚动距离(像素)，默认为视窗高度的一半。\n- `target`: 可选，滚动目标元素，默认为整个页面。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」scroll「末」,\ndirection: 「始」down「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」scroll「末」,\ndirection: 「始」down「末」,\ndistance: 「始」500「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "get_links", "description": "获取当前页面的所有可见链接列表。\n- `command`: 固定为 `get_links`。\n\n返回包含链接文本、URL和vcp-id的详细列表，方便AI识别和点击特定链接。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」get_links「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」get_links「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "debug_page", "description": "获取页面调试信息，包括所有可交互元素的详细信息。\n- `command`: 固定为 `debug_page`。\n\n返回页面结构、元素统计和详细的可交互元素列表，用于调试元素定位问题。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」debug_page「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」debug_page「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "search", "description": "在页面的搜索框中输入搜索内容。自动识别页面上的搜索框并输入指定内容。\n- `command`: 固定为 `search`。\n- `text`: 要搜索的内容。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」search「末」,\ntext: 「始」凡人修仙传「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」search「末」,\ntext: 「始」凡人修仙传「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "select_by_position", "description": "根据位置和类型选择页面元素。支持选择第N个特定类型的元素。\n- `command`: 固定为 `select_by_position`。\n- `type`: 元素类型，如 video(视频), image(图片), link(链接), button(按钮), article(文章)等。\n- `position`: 位置，如 first(第一个), second(第二个), last(最后一个), 或数字(1,2,3...)。\n- `action`: 执行的操作，如 click(点击), info(获取信息)。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」select_by_position「末」,\ntype: 「始」video「末」,\nposition: 「始」first「末」,\naction: 「始」click「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」select_by_position「末」,\ntype: 「始」video「末」,\nposition: 「始」first「末」,\naction: 「始」click「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "click_first_video", "description": "快速点击页面上的第一个视频。这是select_by_position命令的便捷版本。\n- `command`: 固定为 `click_first_video`。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click_first_video「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click_first_video「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "capture_page", "description": "截取当前页面的可视区域截图，让AI能够'看到'页面内容。\n- `command`: 固定为 `capture_page`。\n- `include_elements`: 可选，是否在截图上标注可交互元素，默认为true。\n\n返回base64编码的截图数据，AI可以分析页面视觉内容。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」capture_page「末」,\ninclude_elements: 「始」true「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」capture_page「末」\n<<<[END_TOOL_REQUEST]>>>"}, {"command": "click_at_coordinates", "description": "根据坐标点击页面元素。配合截图功能使用，AI可以看到页面后指定点击位置。\n- `command`: 固定为 `click_at_coordinates`。\n- `x`: X坐标（相对于可视区域）。\n- `y`: Y坐标（相对于可视区域）。\n\n**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click_at_coordinates「末」,\nx: 「始」300「末」,\ny: 「始」200「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "**调用示例:**\n<<<[TOOL_REQUEST]>>>\ntool_name: 「始」ChromeControl「末」,\ncommand: 「始」click_at_coordinates「末」,\nx: 「始」300「末」,\ny: 「始」200「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}