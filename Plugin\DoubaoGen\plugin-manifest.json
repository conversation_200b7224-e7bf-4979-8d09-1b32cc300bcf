{"manifestVersion": "1.0.0", "name": "DoubaoGen", "displayName": "Doubao 风格图片生成器", "version": "0.1.0", "description": "通过火山方舟 API 使用 Doubao Seedream 3.0 模型生成具有特定风格的图片。", "author": "B3000Kcn", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node DoubaoGen.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"VOLCENGINE_API_KEY": "string"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "DoubaoGenerateImage", "description": "调用此工具通过火山方舟 API 的 Doubao Seedream 3.0 模型生成图片。请在您的回复中，使用以下精确格式来请求图片生成，确保所有参数值都用「始」和「末」准确包裹：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」DoubaoGen「末」,\nprompt:「始」(必需) 用于图片生成的详细【英文】提示词。「末」,\nresolution:「始」(必需) 图片分辨率，可选值：「1024x1024」、「864x1152」、「1152x864」、「1280x720」、「720x1280」、「832x1248」、「1248x832」、「1512x648」。「末」\n(可选参数：seed:「始」整数种子「末」 - 通常省略以使用随机种子)\n<<<[END_TOOL_REQUEST]>>>\n\n重要提示给AI：\n当此工具执行完毕后，您将收到包含以下信息的结果：\n1. 生成图片的公开访问URL。\n2. 图片在服务器上的存储相对路径 (例如：image/DoubaoGen/图片名.png)。\n3. 图片的文件名。\n请在您的最终回复中，使用返回的【图片URL】为用户生成一个HTML的 `<img>` 标签来直接展示图片，例如：`<img src=\"[此处填写图片URL]\" alt=\"[此处可填写部分prompt作为描述]\" width=\"300\">`。请确保替换占位符，并可调整 `width` 属性（建议200-500像素）。同时，也可以附带图片的直接URL链接。", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」DoubaoGen「末」,\nprompt:「始」A majestic cat warrior in futuristic armor, standing on a neon-lit city rooftop at night, cyberpunk style.「末」,\nresolution:「始」1024x1024「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}