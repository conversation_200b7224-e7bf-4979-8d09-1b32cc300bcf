import sys
import json
import os
import random
import datetime
import requests
import feedparser

# --- 内置的名言列表 ---
QUOTES = {
    "default": [
        "The only way to do great work is to love what you do. - <PERSON>",
        "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
        "It does not matter how slowly you go as long as you do not stop. - Confu<PERSON>",
        "Everything you can imagine is real. - <PERSON>",
        "The secret of getting ahead is getting started. - <PERSON>"
    ]
}

# --- 模块化渲染函数 ---

def render_weather_module(config):
    """渲染天气模块"""
    api_key = config.get("apiKey")
    api_url = config.get("apiUrl")
    cities = config.get("cities", [])
    
    if not api_key or "YOUR_QWEATHER_API_KEY" in api_key:
        return "**天气**\n  - 天气服务未配置 (请在 config.json 中填写 apiKey)\n"

    weather_lines = ["**天气**"]
    for city in cities:
        try:
            # 1. 获取城市ID
            city_lookup_url = f"https://geoapi.qweather.com/v2/city/lookup?location={city}&key={api_key}"
            city_res = requests.get(city_lookup_url, timeout=5)
            city_res.raise_for_status()
            city_data = city_res.json()
            if city_data.get("code") != "200" or not city_data.get("location"):
                weather_lines.append(f"  - 无法找到城市: {city}")
                continue
            city_id = city_data["location"][0]["id"]

            # 2. 获取天气
            weather_url = f"{api_url}/v7/weather/now?location={city_id}&key={api_key}"
            weather_res = requests.get(weather_url, timeout=5)
            weather_res.raise_for_status()
            weather_data = weather_res.json()
            if weather_data.get("code") != "200":
                weather_lines.append(f"  - {city.capitalize()}: 获取天气失败")
                continue
            
            now = weather_data.get("now", {})
            weather_lines.append(f"  - {city.capitalize()}: {now.get('text', 'N/A')}, {now.get('temp', 'N/A')}°C")
        except requests.RequestException:
            weather_lines.append(f"  - {city.capitalize()}: 请求天气服务失败")
        except Exception:
            weather_lines.append(f"  - {city.capitalize()}: 获取天气时发生未知错误")
    
    return "\n".join(weather_lines) + "\n"

def render_news_module(config):
    """渲染新闻模块"""
    feeds = config.get("feeds", [])
    max_items = config.get("max_items_per_feed", 3)
    
    news_sections = ["**今日头条**"]
    for feed_info in feeds:
        name = feed_info.get("name", "未命名新闻源")
        url = feed_info.get("url")
        news_sections.append(f"  *_{name}_*")
        if not url:
            news_sections.append("    - 新闻源URL未配置")
            continue
        try:
            feed = feedparser.parse(url)
            if feed.bozo:
                news_sections.append("    - 无法解析新闻源，请检查RSS链接")
                continue
            
            entries = feed.entries[:max_items]
            if not entries:
                news_sections.append("    - 此新闻源中没有内容")
                continue

            for entry in entries:
                news_sections.append(f"    - [{entry.title}]({entry.link})")
        except Exception:
            news_sections.append("    - 获取此新闻源时发生错误")
            
    return "\n".join(news_sections) + "\n"

def render_todos_module(config):
    """渲染待办事项模块"""
    file_path = config.get("file_path")
    if not file_path:
        return "**待办事项**\n  - 待办事项文件未配置\n"
    
    abs_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', file_path))

    if not os.path.exists(abs_path):
        try:
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write("# 我的待办事项\n- [ ] 完成每日简报插件的配置\n- [x] 喝一杯咖啡\n")
            return "**待办事项**\n  - 待办事项文件不存在，已为您创建示例 `ToDo.md`\n  - [ ] 完成每日简报插件的配置\n  - [x] 喝一杯咖啡\n"
        except Exception:
            return "**待办事项**\n  - 待办事项文件不存在，且创建失败\n"
    
    try:
        with open(abs_path, 'r', encoding='utf-8') as f:
            todos = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]
        
        if not todos:
            return "**待办事项**\n  - 您的待办事项列表为空\n"
            
        todo_list = "**待办事项**\n" + "\n".join([f"  - {item}" for item in todos])
        return todo_list + "\n"
    except Exception:
        return "**待办事项**\n  - 读取待办事项文件时出错\n"

def render_quote_module(config):
    """渲染每日一句模块"""
    if not config.get("enabled", True):
        return ""
    
    category = config.get("category", "default")
    quote_list = QUOTES.get(category, QUOTES["default"])
    return f"**每日一句**\n  - {random.choice(quote_list)}\n"


# --- 主函数 ---

def main():
    try:
        # 1. 加载配置文件
        config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        if not os.path.exists(config_path):
             raise FileNotFoundError("配置文件 config.json 不存在。请将 config.json.example 复制并重命名为 config.json。")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 2. 定义模块渲染器映射
        renderers = {
            "weather": render_weather_module,
            "news": render_news_module,
            "todos": render_todos_module,
            "quote": render_quote_module
        }

        # 3. 按布局顺序生成简报内容
        layout = config.get("layout", [])
        briefing_parts = []
        today_str = datetime.datetime.now().strftime("%Y年%m月%d日")
        briefing_parts.append(f"### 早上好！这是您的今日简报 ({today_str})\n")

        for module_name in layout:
            if module_name in renderers and module_name in config.get("modules", {}):
                renderer_func = renderers[module_name]
                module_config = config["modules"][module_name]
                briefing_parts.append(renderer_func(module_config))
        
        # 4. 组合并返回结果
        final_briefing = "\n".join(briefing_parts)
        output = {"status": "success", "result": final_briefing.strip()}

    except FileNotFoundError as e:
        output = {"status": "error", "error": str(e)}
    except Exception as e:
        output = {"status": "error", "error": f"生成简报时发生未知错误: {str(e)}"}

    # 5. 将结果打印到 stdout
    print(json.dumps(output, ensure_ascii=False), file=sys.stdout)
    sys.stdout.flush()

if __name__ == "__main__":
    main()