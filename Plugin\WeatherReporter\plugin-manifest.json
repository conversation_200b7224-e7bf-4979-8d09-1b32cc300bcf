{"manifestVersion": "1.0.0", "name": "WeatherReporter", "version": "1.0.0", "displayName": "天气预报员", "description": "提供实时的天气信息，并将其集成到系统提示词的 {{VCPWeatherInfo}} 占位符中。", "author": "System", "pluginType": "static", "entryPoint": {"type": "nodejs", "command": "node weather-reporter.js"}, "communication": {"protocol": "stdio"}, "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{VCPWeatherInfo}}", "description": "当前的实时天气信息。"}], "invocationCommands": []}, "configSchema": {"VarCity": "string", "WeatherKey": "string", "WeatherUrl": "string", "forecastDays": {"type": "integer", "default": 7, "description": "获取未来天气预报的天数 (范围: 1-30)。"}, "hourlyForecastInterval": {"type": "number", "description": "24小时天气预报的显示间隔（小时）", "default": 2, "min": 1}, "hourlyForecastCount": {"type": "number", "description": "24小时天气预报总共显示的条目数。", "default": 12, "min": 1}}, "refreshIntervalCron": "0 */8 * * *"}