# SynapsePusher Plugin Configuration

# Enable debug mode for this plugin (True/False)

DebugMode=False

# VCP_Key for authenticating this plugin (as a client) with the WebSocketServer 

VCP_Key="your_shared_vcplog_websocket_key"

# Synapse Homeserver URL (e.g., https://matrix-client.matrix.org)

SynapseHomeserver="https://matrix-client.matrix.org"

# Synapse Room ID to push messages to (e.g., !yourRoomId:matrix.org)
# Note: This configuration assumes all maids push to the SAME room.
# If different maids need to push to different rooms, the logic and config would need further extension.

SynapseRoomID="!yourRoomId:matrix.org"

# --- Strict Configuration (for normal operation) ---
# JSON string mapping maid names to their specific Synapse Access Tokens.
# Each maid that should send messages MUST have an entry here for strict mode.

MaidAccessTokensJSON='{"XXX":"syt_XXX_TOKEN","YYY":"syt_YYY_TOKEN"}'

# JSON string defining tool_name whitelists for each maid.
# Each maid that should send messages MUST have an entry here for strict mode,
# and the tool_name from the log event must be in this list.

MaidToolWhitelistJSON='{"XXX":["ToolA","ToolB"],"YYY":["ToolB","ToolC","ToolD"]}'

# --- Whitelist Bypass Configuration (for testing ONLY) ---
# If true, the above MaidAccessTokensJSON and MaidToolWhitelistJSON are IGNORED.
# All messages will attempt to be sent using SynapseAccessTokenForTestingOnly.

BypassWhitelistForTesting=False

SynapseAccessTokenForTestingOnly="syt_YOUR_GENERAL_TESTING_TOKEN"