{"manifestVersion": "1.0.0", "name": "1PanelInfoProvider", "version": "1.0.0", "displayName": "1Panel 信息提供器", "description": "从1Panel服务器获取Dashboard基础信息和操作系统信息，并通过独立的系统提示词占位符提供这些数据。", "author": "B3000Kcn", "pluginType": "static", "entryPoint": {"command": "node 1PanelInfoProvider.js"}, "communication": {"protocol": "stdio"}, "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{1PanelDashboard}}", "description": "实时的1Panel仪表盘基础信息 (来自API)。"}, {"placeholder": "{{1PanelOsInfo}}", "description": "实时的1Panel操作系统信息 (来自API)。"}], "invocationCommands": []}, "refreshIntervalCron": "*/10 * * * * *", "configSchema": {"PanelBaseUrl": {"type": "string", "description": "您的1Panel服务器基础URL (例如: http://localhost:12345)", "default": "", "required": true}, "PanelApiKey": {"type": "string", "description": "您的1Panel API密钥", "default": "", "required": true}, "DebugMode": {"type": "boolean", "description": "是否为此插件启用详细的调试日志输出到stderr。", "default": false, "required": false}, "Enabled": {"type": "boolean", "description": "是否启用此1Panel信息提供器插件。如果禁用，插件将不会尝试获取1Panel数据。", "default": true, "required": false}}}