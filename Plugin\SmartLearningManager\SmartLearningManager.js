const fs = require('fs');
const path = require('path');

class SmartLearningManager {
    constructor() {
        this.config = {};
        this.debugMode = false;
        this.knowledgeBase = new Map();
        this.learningPaths = new Map();
        this.progressData = new Map();
    }

    log(message) {
        if (this.debugMode) {
            console.error(`[SmartLearningManager] ${message}`);
        }
    }

    loadConfig(configData) {
        try {
            let config = {};
            if (configData && configData.trim()) {
                try {
                    config = JSON.parse(configData);
                } catch (parseError) {
                    this.log(`Failed to parse config: ${parseError.message}`);
                }
            }

            this.config = config;
            this.debugMode = this.config.DEBUG_MODE === 'true' || this.config.DEBUG_MODE === true;
            
            this.log('Configuration loaded successfully');
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    createKnowledgeMap(topic, content, tags = [], difficulty = 'intermediate', relatedTopics = []) {
        const knowledgeNode = {
            id: this.generateId(),
            topic: topic,
            content: content,
            tags: Array.isArray(tags) ? tags : tags.split(',').map(t => t.trim()),
            difficulty: difficulty,
            relatedTopics: Array.isArray(relatedTopics) ? relatedTopics : relatedTopics.split(',').map(t => t.trim()),
            createdAt: new Date().toISOString(),
            lastReviewed: null,
            masteryLevel: 0
        };

        this.knowledgeBase.set(knowledgeNode.id, knowledgeNode);
        
        let report = `🧠 知识图谱节点创建成功\n\n`;
        report += `📚 主题: ${topic}\n`;
        report += `🏷️ 标签: ${knowledgeNode.tags.join(', ')}\n`;
        report += `📊 难度: ${difficulty}\n`;
        report += `🔗 相关主题: ${knowledgeNode.relatedTopics.join(', ')}\n`;
        report += `🆔 节点ID: ${knowledgeNode.id}\n`;
        report += `⏰ 创建时间: ${knowledgeNode.createdAt}\n\n`;
        report += `📝 内容摘要: ${content.substring(0, 200)}${content.length > 200 ? '...' : ''}\n`;

        return report;
    }

    planLearningPath(goal, currentLevel = 'beginner', timeAvailable = '2小时/天', preferredStyle = 'visual', deadline = null) {
        const pathId = this.generateId();
        const learningPath = {
            id: pathId,
            goal: goal,
            currentLevel: currentLevel,
            timeAvailable: timeAvailable,
            preferredStyle: preferredStyle,
            deadline: deadline,
            createdAt: new Date().toISOString(),
            milestones: this.generateMilestones(goal, currentLevel),
            estimatedDuration: this.estimateDuration(goal, currentLevel, timeAvailable),
            resources: this.suggestResources(goal, preferredStyle)
        };

        this.learningPaths.set(pathId, learningPath);

        let report = `🎯 个性化学习路径规划\n\n`;
        report += `🎯 学习目标: ${goal}\n`;
        report += `📊 当前水平: ${currentLevel}\n`;
        report += `⏰ 可用时间: ${timeAvailable}\n`;
        report += `🎨 学习风格: ${preferredStyle}\n`;
        report += `📅 预计完成时间: ${learningPath.estimatedDuration}\n`;
        if (deadline) report += `⏳ 目标截止时间: ${deadline}\n`;
        report += `🆔 路径ID: ${pathId}\n\n`;

        report += `🗺️ 学习里程碑:\n`;
        learningPath.milestones.forEach((milestone, index) => {
            report += `${index + 1}. ${milestone.title} (${milestone.estimatedTime})\n`;
            report += `   📝 ${milestone.description}\n`;
        });

        report += `\n📚 推荐学习资源:\n`;
        learningPath.resources.forEach(resource => {
            report += `• ${resource.type}: ${resource.title}\n`;
        });

        return report;
    }

    trackProgress(topic, studyTime = 0, comprehensionLevel = 5, notes = '', difficulties = '') {
        const progressId = this.generateId();
        const progressEntry = {
            id: progressId,
            topic: topic,
            studyTime: parseInt(studyTime),
            comprehensionLevel: parseInt(comprehensionLevel),
            notes: notes,
            difficulties: difficulties,
            timestamp: new Date().toISOString(),
            date: new Date().toISOString().split('T')[0]
        };

        this.progressData.set(progressId, progressEntry);

        // 更新知识图谱中的掌握程度
        for (const [id, node] of this.knowledgeBase) {
            if (node.topic === topic) {
                node.masteryLevel = comprehensionLevel;
                node.lastReviewed = progressEntry.timestamp;
                break;
            }
        }

        let report = `📈 学习进度记录\n\n`;
        report += `📚 学习主题: ${topic}\n`;
        report += `⏱️ 学习时长: ${studyTime} 分钟\n`;
        report += `🧠 理解程度: ${comprehensionLevel}/10\n`;
        report += `📅 记录时间: ${progressEntry.timestamp}\n\n`;

        if (notes) {
            report += `📝 学习笔记:\n${notes}\n\n`;
        }

        if (difficulties) {
            report += `❓ 遇到困难:\n${difficulties}\n\n`;
        }

        // 提供学习建议
        report += `💡 学习建议:\n`;
        if (comprehensionLevel < 5) {
            report += `• 理解程度较低，建议重新学习基础概念\n`;
            report += `• 可以尝试不同的学习方法或资源\n`;
        } else if (comprehensionLevel < 8) {
            report += `• 理解程度良好，建议通过练习巩固\n`;
            report += `• 可以开始学习相关的进阶内容\n`;
        } else {
            report += `• 掌握程度很好，可以教授他人或应用到实际项目中\n`;
            report += `• 建议定期复习以保持记忆\n`;
        }

        return report;
    }

    generateQuiz(topic, content = '', questionCount = 10, questionTypes = 'multiple_choice', difficulty = 'medium') {
        const quiz = {
            id: this.generateId(),
            topic: topic,
            questionCount: parseInt(questionCount),
            questionTypes: Array.isArray(questionTypes) ? questionTypes : questionTypes.split(',').map(t => t.trim()),
            difficulty: difficulty,
            createdAt: new Date().toISOString(),
            questions: this.createQuestions(topic, questionCount, questionTypes, difficulty)
        };

        let report = `📝 智能测试生成\n\n`;
        report += `📚 测试主题: ${topic}\n`;
        report += `📊 题目数量: ${questionCount}\n`;
        report += `🎯 题目类型: ${quiz.questionTypes.join(', ')}\n`;
        report += `📈 难度级别: ${difficulty}\n`;
        report += `⏰ 生成时间: ${quiz.createdAt}\n\n`;

        report += `📋 测试题目:\n\n`;
        quiz.questions.forEach((question, index) => {
            report += `${index + 1}. ${question.question}\n`;
            if (question.type === 'multiple_choice') {
                question.options.forEach((option, optIndex) => {
                    report += `   ${String.fromCharCode(65 + optIndex)}. ${option}\n`;
                });
                report += `   正确答案: ${question.correctAnswer}\n`;
            } else if (question.type === 'true_false') {
                report += `   正确答案: ${question.correctAnswer}\n`;
            }
            report += `\n`;
        });

        return report;
    }

    reviewSchedule(topic, lastReviewDate = null, masteryLevel = 5, importance = 'medium') {
        const now = new Date();
        const lastReview = lastReviewDate ? new Date(lastReviewDate) : new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const daysSinceReview = Math.floor((now - lastReview) / (24 * 60 * 60 * 1000));

        // 基于艾宾浩斯遗忘曲线计算复习间隔
        const intervals = this.calculateReviewIntervals(masteryLevel, importance);
        const nextReviewDays = this.getNextReviewInterval(daysSinceReview, intervals);
        const nextReviewDate = new Date(now.getTime() + nextReviewDays * 24 * 60 * 60 * 1000);

        let report = `🔄 智能复习计划\n\n`;
        report += `📚 复习主题: ${topic}\n`;
        report += `📅 上次复习: ${lastReview.toLocaleDateString()}\n`;
        report += `📊 掌握程度: ${masteryLevel}/10\n`;
        report += `⭐ 重要程度: ${importance}\n`;
        report += `📆 距离上次复习: ${daysSinceReview} 天\n\n`;

        report += `🎯 复习建议:\n`;
        if (daysSinceReview >= nextReviewDays) {
            report += `🚨 建议立即复习！已超过最佳复习时间\n`;
        } else {
            report += `📅 建议复习时间: ${nextReviewDate.toLocaleDateString()}\n`;
        }

        report += `\n📋 复习计划:\n`;
        intervals.forEach((interval, index) => {
            const reviewDate = new Date(lastReview.getTime() + interval * 24 * 60 * 60 * 1000);
            const status = daysSinceReview >= interval ? '✅ 已完成' : '⏳ 待复习';
            report += `${index + 1}. ${reviewDate.toLocaleDateString()} (${interval}天后) - ${status}\n`;
        });

        return report;
    }

    analyzeLearningStyle(learningHistory, preferences = '', strengths = '', weaknesses = '') {
        const analysis = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            learningHistory: learningHistory,
            preferences: preferences,
            strengths: strengths,
            weaknesses: weaknesses,
            recommendedStyle: this.determineOptimalStyle(learningHistory, preferences),
            suggestions: this.generateLearningStyleSuggestions(learningHistory, preferences, strengths, weaknesses)
        };

        let report = `🧠 学习风格分析报告\n\n`;
        report += `📊 分析时间: ${analysis.timestamp}\n`;
        report += `🎯 推荐学习风格: ${analysis.recommendedStyle}\n\n`;

        report += `📝 学习历史分析:\n${learningHistory}\n\n`;

        if (preferences) {
            report += `💡 学习偏好:\n${preferences}\n\n`;
        }

        if (strengths) {
            report += `💪 学习优势:\n${strengths}\n\n`;
        }

        if (weaknesses) {
            report += `⚠️ 需要改进:\n${weaknesses}\n\n`;
        }

        report += `🎯 个性化建议:\n`;
        analysis.suggestions.forEach(suggestion => {
            report += `• ${suggestion}\n`;
        });

        return report;
    }

    createStudyPlan(subject, duration, dailyTime = '2小时', goals = '', resources = '') {
        const plan = {
            id: this.generateId(),
            subject: subject,
            duration: duration,
            dailyTime: dailyTime,
            goals: goals,
            resources: resources,
            createdAt: new Date().toISOString(),
            schedule: this.generateStudySchedule(subject, duration, dailyTime),
            milestones: this.generateStudyMilestones(subject, duration)
        };

        let report = `📅 详细学习计划\n\n`;
        report += `📚 学习科目: ${subject}\n`;
        report += `⏰ 学习周期: ${duration}\n`;
        report += `🕐 每日时间: ${dailyTime}\n`;
        report += `🎯 学习目标: ${goals || '待定义'}\n`;
        report += `📖 学习资源: ${resources || '待收集'}\n`;
        report += `📅 创建时间: ${plan.createdAt}\n\n`;

        report += `📋 学习进度安排:\n`;
        plan.schedule.forEach((week, index) => {
            report += `第${index + 1}周: ${week.focus}\n`;
            week.tasks.forEach(task => {
                report += `  • ${task}\n`;
            });
            report += `\n`;
        });

        report += `🎯 重要里程碑:\n`;
        plan.milestones.forEach((milestone, index) => {
            report += `${index + 1}. ${milestone.title} (${milestone.timeframe})\n`;
            report += `   📝 ${milestone.description}\n`;
        });

        return report;
    }

    // 辅助方法
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    generateMilestones(goal, currentLevel) {
        const milestones = [];
        
        if (currentLevel === 'beginner') {
            milestones.push(
                { title: '基础概念掌握', description: '理解核心概念和术语', estimatedTime: '1-2周' },
                { title: '基础技能练习', description: '通过练习巩固基础技能', estimatedTime: '2-3周' },
                { title: '进阶知识学习', description: '学习更复杂的概念和技术', estimatedTime: '3-4周' },
                { title: '实际应用项目', description: '完成实际项目应用所学知识', estimatedTime: '2-3周' }
            );
        } else if (currentLevel === 'intermediate') {
            milestones.push(
                { title: '知识体系梳理', description: '整理和深化现有知识', estimatedTime: '1周' },
                { title: '高级技能学习', description: '掌握高级技术和方法', estimatedTime: '3-4周' },
                { title: '综合项目实践', description: '完成复杂的综合性项目', estimatedTime: '3-4周' }
            );
        }

        return milestones;
    }

    estimateDuration(goal, currentLevel, timeAvailable) {
        const baseHours = currentLevel === 'beginner' ? 100 : currentLevel === 'intermediate' ? 60 : 40;
        const dailyHours = parseFloat(timeAvailable.match(/\d+/)[0]) || 2;
        const days = Math.ceil(baseHours / dailyHours);
        const weeks = Math.ceil(days / 7);
        
        return `约 ${weeks} 周 (${days} 天, ${baseHours} 小时)`;
    }

    suggestResources(goal, preferredStyle) {
        const resources = [];
        
        if (preferredStyle === 'visual') {
            resources.push(
                { type: '视频教程', title: '相关主题的在线视频课程' },
                { type: '图表资料', title: '思维导图和流程图' },
                { type: '交互演示', title: '在线交互式学习平台' }
            );
        } else if (preferredStyle === 'auditory') {
            resources.push(
                { type: '音频课程', title: '播客和音频讲座' },
                { type: '讨论组', title: '学习小组和在线讨论' },
                { type: '语音笔记', title: '录音笔记和口述总结' }
            );
        } else {
            resources.push(
                { type: '书籍教材', title: '经典教材和参考书' },
                { type: '在线课程', title: '结构化的在线学习课程' },
                { type: '实践项目', title: '动手实践项目和练习' }
            );
        }

        return resources;
    }

    createQuestions(topic, count, types, difficulty) {
        const questions = [];
        const questionTypes = Array.isArray(types) ? types : [types];
        
        for (let i = 0; i < count; i++) {
            const type = questionTypes[i % questionTypes.length];
            
            if (type === 'multiple_choice') {
                questions.push({
                    type: 'multiple_choice',
                    question: `关于${topic}的问题 ${i + 1}`,
                    options: ['选项A', '选项B', '选项C', '选项D'],
                    correctAnswer: 'A'
                });
            } else if (type === 'true_false') {
                questions.push({
                    type: 'true_false',
                    question: `${topic}相关的判断题 ${i + 1}`,
                    correctAnswer: '正确'
                });
            } else if (type === 'short_answer') {
                questions.push({
                    type: 'short_answer',
                    question: `请简述${topic}的要点 ${i + 1}`,
                    sampleAnswer: '这是一个示例答案'
                });
            }
        }

        return questions;
    }

    calculateReviewIntervals(masteryLevel, importance) {
        const baseIntervals = [1, 3, 7, 14, 30, 60];
        const masteryMultiplier = masteryLevel / 10;
        const importanceMultiplier = importance === 'high' ? 0.8 : importance === 'low' ? 1.2 : 1.0;
        
        return baseIntervals.map(interval => 
            Math.round(interval * masteryMultiplier * importanceMultiplier)
        );
    }

    getNextReviewInterval(daysSinceReview, intervals) {
        for (const interval of intervals) {
            if (daysSinceReview < interval) {
                return interval;
            }
        }
        return intervals[intervals.length - 1];
    }

    determineOptimalStyle(history, preferences) {
        if (preferences.includes('视频') || preferences.includes('图表')) return '视觉型学习者';
        if (preferences.includes('音频') || preferences.includes('讨论')) return '听觉型学习者';
        if (preferences.includes('实践') || preferences.includes('操作')) return '动觉型学习者';
        return '综合型学习者';
    }

    generateLearningStyleSuggestions(history, preferences, strengths, weaknesses) {
        const suggestions = [
            '根据您的学习历史，建议采用多元化的学习方法',
            '定期进行自我测试以检验学习效果',
            '建立学习小组，通过讨论加深理解',
            '制定明确的学习目标和时间计划'
        ];

        if (weaknesses.includes('注意力')) {
            suggestions.push('尝试番茄工作法，将学习时间分割为小块');
        }

        if (strengths.includes('记忆')) {
            suggestions.push('利用您的记忆优势，多做知识点的关联和总结');
        }

        return suggestions;
    }

    generateStudySchedule(subject, duration, dailyTime) {
        const weeks = parseInt(duration.match(/\d+/)[0]) || 4;
        const schedule = [];

        for (let i = 0; i < weeks; i++) {
            const weekFocus = i === 0 ? '基础概念学习' : 
                            i === weeks - 1 ? '综合复习和应用' : 
                            `第${i + 1}阶段深入学习`;
            
            schedule.push({
                week: i + 1,
                focus: weekFocus,
                tasks: [
                    '理论学习 (40%)',
                    '实践练习 (40%)',
                    '复习总结 (20%)'
                ]
            });
        }

        return schedule;
    }

    generateStudyMilestones(subject, duration) {
        const weeks = parseInt(duration.match(/\d+/)[0]) || 4;
        const milestones = [];

        if (weeks >= 4) {
            milestones.push(
                { title: '基础知识掌握', description: '完成基础概念的学习和理解', timeframe: '第1-2周' },
                { title: '技能应用练习', description: '通过练习项目应用所学知识', timeframe: '第3-4周' }
            );
        }

        if (weeks >= 8) {
            milestones.push(
                { title: '进阶技能掌握', description: '掌握高级技能和方法', timeframe: '第5-6周' },
                { title: '综合项目完成', description: '完成综合性的实际项目', timeframe: '第7-8周' }
            );
        }

        return milestones;
    }

    async processRequest(params) {
        const { command } = params;

        switch (command) {
            case 'create_knowledge_map':
                return this.createKnowledgeMap(
                    params.topic,
                    params.content,
                    params.tags,
                    params.difficulty,
                    params.related_topics
                );

            case 'plan_learning_path':
                return this.planLearningPath(
                    params.goal,
                    params.current_level,
                    params.time_available,
                    params.preferred_style,
                    params.deadline
                );

            case 'track_progress':
                return this.trackProgress(
                    params.topic,
                    params.study_time,
                    params.comprehension_level,
                    params.notes,
                    params.difficulties
                );

            case 'generate_quiz':
                return this.generateQuiz(
                    params.topic,
                    params.content,
                    params.question_count,
                    params.question_types,
                    params.difficulty
                );

            case 'review_schedule':
                return this.reviewSchedule(
                    params.topic,
                    params.last_review_date,
                    params.mastery_level,
                    params.importance
                );

            case 'analyze_learning_style':
                return this.analyzeLearningStyle(
                    params.learning_history,
                    params.preferences,
                    params.strengths,
                    params.weaknesses
                );

            case 'create_study_plan':
                return this.createStudyPlan(
                    params.subject,
                    params.duration,
                    params.daily_time,
                    params.goals,
                    params.resources
                );

            default:
                throw new Error(`未知命令: ${command}`);
        }
    }
}

// 主执行逻辑
async function main() {
    const manager = new SmartLearningManager();
    
    try {
        let inputData = '';
        process.stdin.setEncoding('utf8');
        
        for await (const chunk of process.stdin) {
            inputData += chunk;
        }
        
        if (!inputData.trim()) {
            throw new Error('No input data received');
        }

        const params = JSON.parse(inputData.trim());
        
        manager.loadConfig(JSON.stringify(params.config || {}));
        
        const result = await manager.processRequest(params);
        
        const response = {
            status: 'success',
            result: result
        };

        console.log(JSON.stringify(response));
        
    } catch (error) {
        const response = {
            status: 'error',
            error: error.message,
            messageForAI: `智能学习管理操作失败：${error.message}`
        };

        console.log(JSON.stringify(response));
        process.exit(1);
    }
}

main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
