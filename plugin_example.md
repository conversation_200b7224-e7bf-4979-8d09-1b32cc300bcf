# PoetryMaster 插件实现示例

## 1. plugin-manifest.json
```json
{
  "name": "PoetryMaster",
  "displayName": "诗歌大师",
  "version": "1.0.0",
  "description": "根据主题、情感、风格生成各种诗歌",
  "pluginType": "synchronous",
  "entryPoint": "node poetry-master.js",
  "communication": {
    "protocol": "stdio"
  },
  "configSchema": {
    "ai_model": {
      "type": "string",
      "default": "gemini-2.5-flash",
      "description": "用于生成诗歌的AI模型"
    },
    "enable_image": {
      "type": "boolean", 
      "default": true,
      "description": "是否生成配图"
    }
  },
  "capabilities": {
    "invocationCommands": [
      {
        "command": "generate_poem",
        "description": "根据指定主题、风格和情感生成诗歌。支持古诗、现代诗、打油诗等多种风格。\n\n参数说明：\n- theme: 诗歌主题（必需）\n- style: 诗歌风格（可选：古诗/现代诗/打油诗/自由诗）\n- emotion: 情感色彩（可选：喜悦/忧伤/激昂/宁静）\n- length: 诗歌长度（可选：短/中/长）\n- generate_image: 是否生成配图（可选：true/false）\n\n调用示例：\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Nova「末」\ntool_name:「始」PoetryMaster「末」\ncommand:「始」generate_poem「末」\ntheme:「始」春天的樱花「末」\nstyle:「始」现代诗「末」\nemotion:「始」宁静「末」\nlength:「始」中「末」\ngenerate_image:「始」true「末」\n<<<[END_TOOL_REQUEST]>>>\n\n成功返回格式：\n{\n  \"status\": \"success\",\n  \"result\": {\n    \"poem\": \"生成的诗歌内容\",\n    \"style_analysis\": \"风格分析\",\n    \"image_url\": \"配图URL（如果启用）\"\n  }\n}",
        "example": "为春天写一首现代诗"
      }
    ]
  }
}
```

## 2. poetry-master.js 核心逻辑
```javascript
const fs = require('fs');
const path = require('path');

class PoetryMaster {
    constructor() {
        this.config = this.loadConfig();
        this.poemStyles = {
            '古诗': {
                prompt: '请写一首古体诗，要求对仗工整，平仄协调',
                pattern: '五言或七言律诗格式'
            },
            '现代诗': {
                prompt: '请写一首现代自由诗，注重意境和情感表达',
                pattern: '自由格式，注重韵律美'
            },
            '打油诗': {
                prompt: '请写一首幽默风趣的打油诗',
                pattern: '通俗易懂，朗朗上口'
            }
        };
    }

    async generatePoem(params) {
        const { theme, style = '现代诗', emotion = '宁静', length = '中', generate_image = 'false' } = params;
        
        if (!theme) {
            throw new Error('主题参数是必需的');
        }

        // 构建诗歌生成prompt
        const styleConfig = this.poemStyles[style] || this.poemStyles['现代诗'];
        const lengthMap = { '短': '4-8行', '中': '8-16行', '长': '16-32行' };
        
        const prompt = `
请根据以下要求创作一首诗歌：
- 主题：${theme}
- 风格：${style}（${styleConfig.prompt}）
- 情感：${emotion}
- 长度：${lengthMap[length] || '8-16行'}

要求：
1. 诗歌内容要贴合主题，情感真挚
2. 语言优美，富有诗意
3. 符合${style}的特点
4. 体现${emotion}的情感色彩

请直接返回诗歌内容，不需要额外说明。
        `.trim();

        try {
            const poem = await this.callAIForPoetry(prompt);
            
            let result = {
                poem: poem,
                style_analysis: `这是一首${style}，以"${theme}"为主题，表达了${emotion}的情感。`,
                metadata: {
                    theme, style, emotion, length,
                    created_at: new Date().toISOString()
                }
            };

            // 如果启用配图生成
            if (generate_image === 'true' && this.config.enable_image !== 'false') {
                const imageUrl = await this.generatePoemImage(theme, poem);
                if (imageUrl) {
                    result.image_url = imageUrl;
                }
            }

            return {
                status: 'success',
                result: result,
                messageForAI: `已成功生成${style}《${theme}》，诗歌富有${emotion}的情感色彩。${generate_image === 'true' ? '并已生成配图。' : ''}`
            };

        } catch (error) {
            return {
                status: 'error',
                error: `诗歌生成失败: ${error.message}`,
                messageForAI: '抱歉，诗歌生成过程中遇到了问题，请稍后重试。'
            };
        }
    }
}
```

## 实现要点

### ✅ 完全可行的功能
1. **文本生成**: 调用AI API生成诗歌内容
2. **风格控制**: 通过prompt engineering实现不同诗歌风格
3. **参数化**: 支持主题、风格、情感、长度等参数
4. **配图集成**: 可以调用FluxGen等图像生成插件
5. **数据存储**: 保存生成的诗歌和元数据

### 🔧 技术实现
1. **标准VCP插件结构**: 遵循VCP插件开发规范
2. **错误处理**: 完善的错误处理和用户反馈
3. **配置管理**: 支持插件专属配置
4. **扩展性**: 易于添加新的诗歌风格和功能

### 📈 进阶功能
1. **诗歌评分**: 分析诗歌质量和特点
2. **历史记录**: 保存用户的诗歌创作历史
3. **风格学习**: 学习特定诗人的写作风格
4. **音频朗诵**: 集成TTS生成诗歌朗诵
