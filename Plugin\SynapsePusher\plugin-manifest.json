{"name": "Synapse<PERSON><PERSON>er", "displayName": "VCP 日志 Synapse 推送器", "version": "1.0.0", "description": "将 VCP 工具调用日志实时推送到指定的 Synapse (Matrix) 房间。", "pluginType": "service", "entryPoint": {"script": "SynapsePusher.js"}, "communication": {"protocol": "direct"}, "configSchema": {"DebugMode": "boolean", "VCP_Key": "string", "SynapseHomeserver": "string", "SynapseRoomID": "string", "MaidAccessTokensJSON": "string", "MaidToolWhitelistJSON": "string", "BypassWhitelistForTesting": "boolean", "SynapseAccessTokenForTestingOnly": "string"}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": []}}