const PluginManagerCenter = require('./PluginManagerCenter');

async function testCheckDependencies() {
    console.log('🧪 Testing PluginManagerCenter check_dependencies...');
    
    const manager = new PluginManagerCenter();
    
    try {
        const result = await manager.processRequest({
            command: 'check_dependencies'
        });
        
        console.log('✅ Test Result:');
        console.log(JSON.stringify(result, null, 2));
        
        if (result.status === 'success') {
            console.log('🎉 check_dependencies command works correctly!');
            const pluginCount = Object.keys(result.result).length;
            console.log(`📊 Checked ${pluginCount} plugins`);
        } else {
            console.log('❌ check_dependencies failed:', result.error);
        }
        
    } catch (error) {
        console.log('💥 Test failed with error:', error.message);
    }
}

async function testSpecificPlugin() {
    console.log('\n🧪 Testing specific plugin dependency check...');
    
    const manager = new PluginManagerCenter();
    
    try {
        const result = await manager.processRequest({
            command: 'check_dependencies',
            plugin_name: 'SciCalculator'
        });
        
        console.log('✅ Specific Plugin Test Result:');
        console.log(JSON.stringify(result, null, 2));
        
    } catch (error) {
        console.log('💥 Specific plugin test failed:', error.message);
    }
}

async function main() {
    await testCheckDependencies();
    await testSpecificPlugin();
}

main().catch(console.error);
