{"manifestVersion": "1.0.0", "name": "DailyBriefing", "version": "1.0.0", "displayName": "每日个人简报", "description": "一个为用户生成每日定制简报的插件。当用户以“早上好”、“good morning”或类似的问候开始对话时，你应该自动调用此工具，无需任何额外参数，为用户提供今日的专属信息汇总。", "author": "Your Assistant", "pluginType": "synchronous", "entryPoint": {"type": "python", "command": "python DailyBriefing.py"}, "communication": {"protocol": "stdio", "timeout": 20000}, "capabilities": {"invocationCommands": [{"commandIdentifier": "GenerateBriefing", "description": "当用户发出早安问候时，调用此工具以生成他们的每日简报。此工具不需要任何参数。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」DailyBriefing「末」\n<<<[END_TOOL_REQUEST]>>>", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」DailyBriefing「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}