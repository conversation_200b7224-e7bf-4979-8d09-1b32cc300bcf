# PluginManagerCenter - 插件管理中心

一个用于集中管理VCPToolBox插件的强大工具，提供插件状态监控、配置管理、性能统计等功能。

## 🚀 功能特性

### 核心功能
- **插件状态监控** - 实时查看所有插件的运行状态
- **动态启用/禁用** - 无需重启服务即可控制插件可用性
- **性能统计** - 监控插件调用次数、耗时、成功率等指标
- **依赖检查** - 自动检查插件依赖是否满足
- **配置管理** - 统一管理插件配置参数
- **批量操作** - 支持对多个插件进行批量管理

### 高级特性
- **智能过滤** - 按状态、类型等条件筛选插件
- **详细报告** - 生成插件健康状况详细报告
- **历史统计** - 支持不同时间范围的统计分析
- **错误诊断** - 自动诊断插件问题并提供解决建议

## 📦 安装配置

### 1. 配置文件
复制配置文件模板：
```bash
cp config.env.example config.env
```

编辑 `config.env` 文件，根据需要调整配置：
```env
ENABLE_PERFORMANCE_MONITORING=true
AUTO_CHECK_DEPENDENCIES=true
STATS_RETENTION_DAYS=30
```

### 2. 依赖安装
本插件使用Node.js开发，确保已安装Node.js环境。

## 🎯 使用方法

### 基本命令

#### 1. 查看所有插件
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」list_plugins「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 2. 查看特定插件状态
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」plugin_status「末」
plugin_name:「始」SciCalculator「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 3. 启用/禁用插件
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」toggle_plugin「末」
plugin_name:「始」SciCalculator「末」
action:「始」disable「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 4. 查看性能统计
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」plugin_stats「末」
time_range:「始」7d「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 5. 检查依赖
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」check_dependencies「末」
<<<[END_TOOL_REQUEST]>>>
```

### 高级用法

#### 过滤插件列表
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」list_plugins「末」
filter:「始」enabled「末」
detailed:「始」true「末」
<<<[END_TOOL_REQUEST]>>>
```

#### 查看特定插件统计
```
<<<[TOOL_REQUEST]>>>
maid:「始」Nova「末」
tool_name:「始」PluginManagerCenter「末」
command:「始」plugin_stats「末」
plugin_name:「始」OpenAIImageGen「末」
time_range:「始」30d「末」
<<<[END_TOOL_REQUEST]>>>
```

## 📊 输出格式

### 插件列表输出
```json
{
  "status": "success",
  "result": {
    "total": 25,
    "filtered": 20,
    "plugins": [
      {
        "name": "SciCalculator",
        "displayName": "科学计算器",
        "version": "1.1.1",
        "status": "enabled",
        "lastCall": "2025-01-15T10:30:00Z",
        "totalCalls": 156
      }
    ]
  }
}
```

### 插件状态输出
```json
{
  "status": "success",
  "result": {
    "name": "SciCalculator",
    "displayName": "科学计算器",
    "version": "1.1.1",
    "status": "enabled",
    "dependencies": {
      "satisfied": true,
      "missing": [],
      "errors": []
    },
    "stats": {
      "calls": 156,
      "totalTime": 2340,
      "errors": 2,
      "lastCall": "2025-01-15T10:30:00Z"
    }
  }
}
```

## 🔧 故障排除

### 常见问题

1. **插件状态显示错误**
   - 检查插件的 `plugin-manifest.json` 文件是否正确
   - 确认插件目录结构完整

2. **依赖检查失败**
   - 确保Python/Node.js环境正确安装
   - 检查插件所需的库是否已安装

3. **性能统计不准确**
   - 确认 `ENABLE_PERFORMANCE_MONITORING` 已启用
   - 检查统计文件权限

### 调试模式
启用调试模式获取详细信息：
```env
DebugMode=true
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个插件！

## 📄 许可证

本项目遵循VCPToolBox的许可证协议。
