console.log('VCPChrome background.js loaded.');
let ws = null;
let isConnected = false;
let reconnectAttempts = 0;
let maxReconnectAttempts = 5;
let reconnectInterval = 1000; // 初始重连间隔1秒
let reconnectTimer = null;
let heartbeatTimer = null;
let isManualDisconnect = false;
const defaultServerUrl = 'ws://localhost:8088'; // 默认服务器地址
const defaultVcpKey = 'your_secret_key'; // 默认密钥

function connect() {
    if (ws && ws.readyState === WebSocket.OPEN) {
        console.log('WebSocket is already connected.');
        return;
    }

    // 清除重连定时器
    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
    }

    // 从storage获取URL和Key
    chrome.storage.local.get(['serverUrl', 'vcpKey'], (result) => {
        const serverUrlToUse = result.serverUrl || defaultServerUrl;
        const keyToUse = result.vcpKey || defaultVcpKey;

        const fullUrl = `${serverUrlToUse}/vcp-chrome-observer/VCP_Key=${keyToUse}`;
        console.log(`Connecting to: ${fullUrl} (attempt ${reconnectAttempts + 1})`);

        ws = new WebSocket(fullUrl);

        ws.onopen = () => {
            console.log('WebSocket connection established.');
            isConnected = true;
            reconnectAttempts = 0; // 重置重连计数
            reconnectInterval = 1000; // 重置重连间隔
            isManualDisconnect = false;
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态
            startHeartbeat(); // 启动心跳检测
        };

        ws.onmessage = (event) => {
            console.log('Message from server:', event.data);
            const message = JSON.parse(event.data);
            
            // 处理来自服务器的指令
            if (message.type === 'command') {
                const commandData = message.data;
                console.log('Received commandData:', commandData);
                // 检查是否是 open_url 指令
                if (commandData.command === 'open_url' && commandData.url) {
                    console.log('Handling open_url command. URL:', commandData.url);
                    let fullUrl = commandData.url;
                    if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
                        fullUrl = 'https://' + fullUrl;
                    }
                    console.log('Attempting to create tab with URL:', fullUrl);
                    chrome.tabs.create({ url: fullUrl }, (tab) => {
                        if (chrome.runtime.lastError) {
                            const errorMessage = `创建标签页失败: ${chrome.runtime.lastError.message}`;
                            console.error('Error creating tab:', errorMessage);
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: 'command_result',
                                    data: {
                                        requestId: commandData.requestId,
                                        status: 'error',
                                        error: errorMessage
                                    }
                                }));
                            }
                        } else {
                            console.log('Tab created successfully. Tab ID:', tab.id, 'URL:', tab.url);
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: 'command_result',
                                    data: {
                                        requestId: commandData.requestId,
                                        sourceClientId: commandData.sourceClientId, // 确保返回 sourceClientId
                                        status: 'success',
                                        message: `成功打开URL: ${commandData.url}`
                                    }
                                }));
                            }
                        }
                    });
                } else {
                    console.log('Forwarding command to content script:', commandData);
                    forwardCommandToContentScript(commandData);
                }
            }
        };

        ws.onclose = (event) => {
            console.log(`WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}`);
            isConnected = false;
            ws = null;
            stopHeartbeat();
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态

            // 如果不是手动断开，尝试重连
            if (!isManualDisconnect) {
                scheduleReconnect();
            }
        };

        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            isConnected = false;
            ws = null;
            stopHeartbeat();
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态

            // 连接错误时也尝试重连
            if (!isManualDisconnect) {
                scheduleReconnect();
            }
        };
    });
}

function disconnect() {
    isManualDisconnect = true; // 标记为手动断开
    reconnectAttempts = 0; // 重置重连计数

    // 清除所有定时器
    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
    }
    stopHeartbeat();

    if (ws) {
        ws.close();
    }
}

function updateIcon() {
    const iconPath = isConnected ? 'icons/icon48.png' : 'icons/icon_disconnected.png'; // 你需要创建一个断开连接的图标
    // 为了简单起见，我们先只改变徽章
    chrome.action.setBadgeText({ text: isConnected ? 'On' : 'Off' });
    chrome.action.setBadgeBackgroundColor({ color: isConnected ? '#00C853' : '#FF5252' });
}

// 监听来自popup和content_script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'GET_STATUS') {
        sendResponse({ isConnected: isConnected });
    } else if (request.type === 'TOGGLE_CONNECTION') {
        if (isConnected) {
            disconnect();
        } else {
            connect();
        }
        // 不再立即返回状态，而是等待广播
        // sendResponse({ isConnected: !isConnected });
    } else if (request.type === 'PAGE_INFO_UPDATE') {
        // 从content_script接收到页面信息，发送到服务器
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'pageInfoUpdate',
                data: { markdown: request.data.markdown }
            }));
        }
    } else if (request.type === 'COMMAND_RESULT') {
        // 从content_script接收到命令执行结果，发送到服务器
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'command_result',
                data: request.data
            }));
        }
    }
    return true; // 保持消息通道开放以进行异步响应
});

function forwardCommandToContentScript(commandData) {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
                type: 'EXECUTE_COMMAND',
                data: commandData
            }, (response) => {
                // 处理Content Script无响应的情况
                if (chrome.runtime.lastError) {
                    console.error('Failed to send command to content script:', chrome.runtime.lastError.message);

                    // 向服务器返回错误信息
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'command_result',
                            data: {
                                requestId: commandData.requestId,
                                sourceClientId: commandData.sourceClientId,
                                status: 'error',
                                error: `Content script not available: ${chrome.runtime.lastError.message}`
                            }
                        }));
                    }
                }
            });
        } else {
            console.error('No active tab found');
            // 向服务器返回错误信息
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'command_result',
                    data: {
                        requestId: commandData.requestId,
                        sourceClientId: commandData.sourceClientId,
                        status: 'error',
                        error: 'No active tab found'
                    }
                }));
            }
        }
    });
}

function broadcastStatusUpdate() {
    chrome.runtime.sendMessage({
        type: 'STATUS_UPDATE',
        isConnected: isConnected
    }).catch(error => {
        // 捕获当popup未打开时发送消息产生的错误，这是正常现象
        if (error.message.includes("Could not establish connection. Receiving end does not exist.")) {
            // This is expected if the popup is not open.
        } else {
            console.error("Error broadcasting status:", error);
        }
    });
}

// 监听标签页切换
chrome.tabs.onActivated.addListener((activeInfo) => {
    // 请求新激活的标签页更新信息
    chrome.tabs.sendMessage(activeInfo.tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {
        if (!e.message.includes("Could not establish connection")) console.log("Error sending to content script on tab activation:", e.message);
    });
});

// 监听标签页URL变化或加载状态变化
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当导航开始时，清除内容脚本的状态以防止内容累积
    if (changeInfo.status === 'loading') {
        chrome.tabs.sendMessage(tabId, { type: 'CLEAR_STATE' }).catch(e => {
            // This error is expected if the content script hasn't been injected yet
            if (!e.message.includes("Could not establish connection")) console.log("Error sending CLEAR_STATE:", e.message);
        });
    }
    // 当页面加载完成时，或者URL变化后加载完成时，请求更新
    if (changeInfo.status === 'complete' && tab.active) {
        chrome.tabs.sendMessage(tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {
            if (!e.message.includes("Could not establish connection")) console.log("Error sending to content script on tab update:", e.message);
        });
    }
});

// 重连调度函数
function scheduleReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
        console.log('Max reconnect attempts reached. Stopping reconnection.');
        return;
    }

    reconnectAttempts++;
    const delay = Math.min(reconnectInterval * Math.pow(2, reconnectAttempts - 1), 30000); // 指数退避，最大30秒

    console.log(`Scheduling reconnect in ${delay}ms (attempt ${reconnectAttempts}/${maxReconnectAttempts})`);

    reconnectTimer = setTimeout(() => {
        if (!isConnected && !isManualDisconnect) {
            connect();
        }
    }, delay);
}

// 心跳检测
function startHeartbeat() {
    stopHeartbeat(); // 确保没有重复的心跳
    heartbeatTimer = setInterval(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            try {
                ws.send(JSON.stringify({ type: 'ping' }));
            } catch (error) {
                console.error('Failed to send heartbeat:', error);
                // 心跳发送失败，可能连接已断开
                if (!isManualDisconnect) {
                    scheduleReconnect();
                }
            }
        }
    }, 30000); // 每30秒发送一次心跳
}

function stopHeartbeat() {
    if (heartbeatTimer) {
        clearInterval(heartbeatTimer);
        heartbeatTimer = null;
    }
}

// 连接状态检查
function checkConnectionHealth() {
    if (!isConnected && !isManualDisconnect && reconnectAttempts < maxReconnectAttempts) {
        console.log('Connection health check: attempting to reconnect');
        connect();
    }
}

// 定期检查连接健康状态
setInterval(checkConnectionHealth, 60000); // 每分钟检查一次

// 扩展启动时的初始化
chrome.runtime.onStartup.addListener(() => {
    console.log('Extension startup detected');
    // 延迟一段时间后尝试连接，确保系统稳定
    setTimeout(() => {
        chrome.storage.local.get(['autoConnect'], (result) => {
            if (result.autoConnect !== false) { // 默认自动连接
                connect();
            }
        });
    }, 2000);
});

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed/updated:', details.reason);
    updateIcon();

    // 设置默认配置
    chrome.storage.local.set({
        autoConnect: true,
        serverUrl: defaultServerUrl,
        vcpKey: defaultVcpKey
    });
});

// 监听扩展挂起和恢复
chrome.runtime.onSuspend.addListener(() => {
    console.log('Extension suspending, cleaning up...');
    disconnect();
});

// 初始化图标状态
updateIcon();

// 启动时自动连接检查
chrome.storage.local.get(['autoConnect'], (result) => {
    if (result.autoConnect !== false) {
        setTimeout(connect, 1000); // 延迟1秒后连接
    }
});