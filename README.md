# VCP (Variable & Command Protocol) - 新一代 AI 能力增强与进化中间层

![LOGO图片1](VCPLogo.png)

---

[English](README_en.md) | [日本語](README_ja.md) | [Русский](README_ru.md) 


---

## 1. 项目愿景：超越交互，赋能进化

VCP (Variable & Command Protocol) 旨在构建一个超越传统 AI 交互模式的革命性中间层。它不仅是一个高度兼容、通用、可扩展的工具箱，更是一个致力于赋能 AI 模型实现能力跃迁、记忆进化与群体智能涌现的生态系统。

我们的目标是创建一个**“不受模型种类、模态、功能限制”且“不受前端交互束缚”**的通用 VCP 系统，通过在 API 层面深度整合 AI 推理、外部工具执行与持久化记忆系统，形成高效协同的“AI-工具-记忆”铁三角。VCP 允许 AI 之间、AI 与工具之间、甚至 AI 与自身记忆之间进行前所未有的高效通讯与协同进化，一切基于私有和共享知识库实现，从而无限扩展 AI 的应用边界与智能潜力。

VCP 的核心是引领 AI Agent 进入一个能够自主学习、持续进化、并能与其他 Agent 高效协作的新纪元。

[深入理解 VCP 理论架构与核心洞见](VCP.md)

**郑重警告**：请勿使用任何非官方或反向代理的 API (例如各类“镜像站”、“中转API服务商”) 来调用此工具箱，VCP拥有几乎底层的系统监控权限，此类行为极易导致您的敏感信息（包括但不限于 AI 交互数据、记忆库内容、API密钥、浏览器历史、账号密码）泄露给不可信的第三方，造成不可挽回的损失。请务必确保您的 AI 模型 API 调用链路纯净、直接、可信。

## 2. VCP 的“次时代”核心特性与设计哲学

VCP 的每一项特性都根植于其前瞻性的设计哲学，旨在解决当前 AI 应用的深层痛点，并引领下一代 AI Agent 的发展方向。

### 极其强大的插件化架构

- **设计哲学**: 实现极致的灵活性与可扩展性，让 AI 的能力可以无限延伸。通过定义良好的插件清单 (`plugin-manifest.json`) 和核心插件管理器 (`Plugin.js`)，VCP 能够轻松集成和管理任何外部功能模块，无论是现有 API、专业软件、硬件接口还是自定义逻辑。
- **深度解读**: 这不仅仅是功能扩展，更是构建了一个 AI 可自主“学习”和“掌握”新技能的生态基础。

### VCP 指令协议

- **设计哲学**: 提供一种对 AI 模型友好、对前端零侵入、且能承载复杂指令的通用工具调用语言。AI 通过在回复中嵌入特定格式的指令 (`<<<[TOOL_REQUEST]>>> ... <<<[END_TOOL_REQUEST]>>>`) 来调用插件，参数使用 `key:「始」value「末」` 格式，支持复杂数据类型和多行文本。
- **深度解读**: 这种基于文本标记的协议，极大地降低了对特定模型 API 特性（如某些模型专属的 Function Calling 字段）的依赖，实现了真正的模型普适性。独特的 `「始」value「末」` 参数格式，则保证了在复杂参数传递（如代码块、JSON对象、长文本）时的解析鲁棒性。此外，协议实现了强大的鲁棒性修复。参数键（key）的解析不仅大小写不敏感，还会自动忽略下划线、连字符等常见分隔符。这意味着 AI 在生成指令时，无论是 `image_size`、`imagesize`、`ImageSize` 还是 `IMAGE-SIZE`，都会被正确识别为同一个参数，极大地增强了协议的容错性和对 AI 生成内容多样性的适应能力。

### 统一的 WebSocket 通信服务

- **设计哲学**: 提供一个集中的 WebSocket 服务 (`WebSocketServer.js`)，用于服务器与客户端之间的双向实时通信。这包括但不限于推送日志、AI 生成的消息、状态更新等。
- **核心能力**:
    - **集中管理**: 所有 WebSocket 连接、认证、消息广播由 `WebSocketServer.js` 统一处理。
    - **插件集成**: 服务类插件（如 `VCPLog`）和同步插件（通过 `webSocketPush` 配置）可以利用此中央服务向客户端推送信息，而无需各自实现 WebSocket 服务器。
    - **客户端类型**: 支持基于 `clientType` 的消息定向广播，允许不同前端或客户端组件订阅特定类型的消息。
- **深度解读**: 简化了需要实时推送功能的插件开发，提高了系统的模块化和可维护性。它也是 VCP 分布式网络的核心通信骨架。

### 模型白名单穿透机制

- **设计哲学**: 承认并兼容并非所有 AI 模型都遵循标准聊天API格式。对于某些特殊模型（如文生图、向量化模型），其API请求/响应结构完全不同，需要绕过VCP的标准化处理流程。
- **核心能力**:
    - **配置驱动**: 在 `config.env` 中通过 `WhitelistImageModel` 和 `WhitelistEmbeddingModel` 变量分别配置图像模型和向量化模型的白名单（用逗号分隔）。
    - **请求绕行**: 当服务器收到一个针对白名单内模型的请求时，它会通过一个特殊的路由模块 (`specialModelRouter.js`) 进行处理，**完全跳过**所有的常规处理步骤（包括消息预处理、变量替换、工具调用循环等）。
    - **直接转发**:
        - **向量化模型**: 请求体和响应体将在客户端与上游API之间被原封不动地直接转发，实现真正的“穿透”。
        - **图像模型**: 服务器会在转发前对请求体进行必要的修改（如添加特定的 `generationConfig`），并根据需要处理响应。
- **深度解读**: 该机制保证了VCP对未来或非标准AI模型的最大兼容性，使其可以作为一个纯粹、高效的代理，而不是处理流程中的障碍。

### 丰富的插件类型，支撑 AI 全方位能力拓展

VCP 支持多种插件类型，以满足不同的 AI 能力扩展需求。核心的交互型插件主要分为同步阻塞型和异步回调型。

- **静态插件 (static)**:
  - **作用**: 为 AI 提供动态的、实时的“世界知识”，如天气、自定义数据、角色日记等，通过替换系统提示词中的占位符注入。支持定时刷新。
  - **次时代意义**: 克服 LLM 知识截止日期限制，赋予 AI 实时感知能力，是构建“情境感知型 AI”的关键。
- **消息预处理器插件 (messagePreprocessor)**:
  - **作用**: 在用户请求发送给 AI 模型前，对消息内容进行修改或增强，如图像识别与描述 (`ImageProcessor`)。
  - **次时代意义**: 实现多模态输入的统一处理，让 AI 能够“理解”更丰富的输入信息，是构建多模态智能体的基础。
- **同步插件 (synchronous)**:
  - **模式**: 这是传统的阻塞式调用。AI 发起调用后，VCP 服务器会启动插件进程，并**等待该插件进程完全执行完毕**后，收集其标准输出作为结果，然后将此结果反馈给 AI 进行后续处理。
  - **适用场景**: 适用于执行时间较短、能够快速返回结果的任务，如科学计算 (`SciCalculator`)、简单的信息查询等。
  - **WebSocket 推送**: 部分同步插件的最终结果也可以通过在其 `plugin-manifest.json` 中配置 `webSocketPush`，经由统一的 WebSocket 服务推送给客户端。
  - **次时代意义**: 这是 AI “行动能力”的基础体现，使其能够干预外部世界、获取外部信息。
- **异步插件 (asynchronous)**:
  - **核心模式**: 专为耗时任务设计，实现非阻塞调用和AI对结果的“即时”感知。
    1.  **AI调用与初始响应**: AI调用异步插件。插件脚本立即向标准输出打印一个JSON响应，此响应通常包含一个唯一的任务ID，以及一段**引导AI使用的自然语言消息**。这条消息会指示AI在其回复中包含一个特定格式的动态占位符，如 `{{VCP_ASYNC_RESULT::PluginName::ActualTaskID}}`。`Plugin.js`捕获此初始响应并迅速返回给AI。
    2.  **AI回复与占位符植入**: AI根据插件的初始响应（包含引导文本和占位符格式）生成回复给用户的消息，并将该占位符原文包含在其回复中。
    3.  **插件后台执行**: 与此同时，插件的非守护线程或后台进程继续执行耗时的主要任务（例如视频生成）。
    4.  **任务完成回调与结果持久化**: 插件后台任务完成后，它会向VCP服务器的 `/plugin-callback/:pluginName/:taskId` 端点发送一个HTTP POST请求。请求体是包含任务最终结果的JSON数据。服务器在收到此回调后，会将这个JSON数据保存到服务器本地的一个特定文件（例如 `VCPAsyncResults/PluginName-ActualTaskID.json`）。
    5.  **动态上下文替换与AI感知**: 当AI的后续对话（无论是用户输入中引用了AI之前的回复，还是AI在生成新回复时参考了历史对话）中再次出现 `{{VCP_ASYNC_RESULT::PluginName::ActualTaskID}}` 这个占位符时，VCP服务器的 `replaceCommonVariables` 函数会在将此上下文发送给AI模型之前，动态地查找对应的结果文件：
        - 如果结果文件存在，占位符会被替换为文件中的实际结果信息（例如，一段描述任务成功并包含URL的文本）。
        - 如果结果文件不存在（任务仍在处理中），占位符会被替换为一个提示信息（如“[任务结果待更新...]”）。
        这样，AI在其后续的“思考”和回复中，就能“看到”异步任务的最新状态或最终结果，仿佛信息是即时更新在对话流中的。
    6.  **WebSocket 推送 (可选并行机制)**: 服务器在收到插件回调并持久化结果后，仍然可以（并且推荐）根据插件清单中的 `webSocketPush` 配置，通过 `WebSocketServer.js` 将回调数据（或其一部分）推送给客户端，作为一种更直接、更即时的前端通知方式。这种基于文件的上下文替换机制是对WebSocket推送的补充和增强，尤其是在AI需要直接感知和利用异步结果进行后续对话时。
  - **适用场景**: 非常适合执行时间较长的任务，如视频生成 (`Wan2.1VideoGen`)、复杂数据分析、需要轮询外部API状态等。
  - **优势**:
      - AI无需长时间等待，交互流畅。
      - AI能够在其对话流中“自然地”感知和利用异步任务的最终结果。
      - 降低了对前端必须依赖WebSocket来获取和回传异步结果的硬性要求。
      - 提升了系统的鲁棒性，即使WebSocket通知失败或客户端未连接，AI仍有机会通过上下文感知结果。
  - **次时代意义**: 赋予AI执行复杂、耗时任务而自身不被阻塞的能力，结合上下文感知的异步结果和可选的即时通知，极大地提升了用户体验和AI Agent的自主性与智能表现。
- **服务插件 (service)**:
  - **作用**: 允许插件向主应用注册独立的 HTTP 路由，提供额外的服务接口，如图床服务 (`ImageServer`)。部分服务插件（如 `VCPLog`）也可能利用统一 WebSocket 服务进行信息推送。
  - **次时代意义**: 将 VCP 平台本身转变为一个可扩展的服务中心，支持更复杂的应用场景。
- **混合服务插件 (hybridservice)**:
  - **作用**: 这是 `service` 和 `messagePreprocessor` 插件的结合体。它既可以像消息预处理器一样在 AI 请求前修改消息流，又可以像服务插件一样注册自己的 API 路由来提供后台服务。
  - **适用场景**: 非常适合那些既需要后台管理界面（通过 API 路由实现），又需要直接干预聊天上下文的复杂插件，例如 `VCPTavern` 上下文注入器。
  - **次时代意义**: 为构建功能更完整、更自洽的复杂插件提供了可能，使得插件本身就能形成一个包含“前端逻辑（预处理）”和“后端管理（服务）”的迷你应用。
- **分布式节点 (Distributed Node)**:
- **作用**: 允许将 VCP 的算力扩展到多台机器。一个独立的 `VCPDistributedServer` 节点可以运行在任何地方，加载其本地的插件，并通过 WebSocket 连接到主服务器。
- **次时代意义**: 这是 VCP 实现“无限算力”和“群体智能”的物理基础。通过分布式网络，可以将需要大量计算资源（如 GPU 密集型任务）或需要访问特定网络/硬件（如内网文件服务器、特殊硬件）的插件部署在最合适的机器上，而主服务器则作为统一的调度中心，对 AI 透明地调用这些远程资源。

### 全局多模态智能路由与数据链

- **设计哲学**: 打破模态壁垒，实现任意数据类型在 VCP 网络中的无缝流转、智能转译与高效处理，构建一个真正意义上的全媒体智能生态。
- **核心能力**:
    - **Base64 直通车**: 允许 AI 在 `tool` 字段中直接引入 Base64 数据，极大地简化了多媒体内容的即时调用。
    - **全局文件 API (`VCPFileAPI`)**: 提供统一的接口，用于处理和中转各类多媒体文件，是多模态数据流转的核心枢纽。
    - **跨模态智能转译**: 实现高阶模型对低阶模型的“能力赋能”。例如，能识别音频的模型可以帮助纯文本模型，将其无法处理的音频 Base64 数据智能转译为文字描述，反哺给请求方。
    - **分布式多模态传输**: 在 VCP 分布式节点之间建立起高效的多模态数据传输链，确保 Base64 等大数据块能够在不同服务器间可靠传递。
    - **智能响应路由**: VCP 核心能够智能判断插件返回的是传统的 stdio 文本信息，还是包含 Base64 的结构化数据，并自动选择正确的渠道进行转发和处理。
    - **多 Agent 协作共享**: 在多 Agent 协同任务中，实现 Base64 数据的智能共享，并能根据需要将其动态转译为临时的 `fileurl`，方便其他 Agent 或前端应用访问。
- **深度解读**: 这套垂直打通的架构，意味着 VCP 不再仅仅处理文本指令，而是进化成了一个能够理解、处理和传递图像、音频、视频等多种媒体格式的“全模态操作系统”。它为开发能够看、听、说的复杂 AI Agent 提供了坚实的基础设施。一个梦幻般的应用场景是：AI Agent 可以通过一个部署在远程服务器上的 `FileManager` 插件，直接读取 WebDAV 上的多媒体文件（如会议录音、视频片段），获取其 Base64 数据流，并对其进行即时分析和总结，整个过程对 AI 来说如同读取本地文件一样自然。
 
### 标准化定时任务调度

- **设计哲学**: 赋予 AI Agent 规划和执行未来任务的能力，将“即时交互”扩展到“跨时域工作流”。VCP 实现了一个通用的、标准化的定时任务系统，允许任何插件请求在未来的特定时间点执行一个标准的 VCP Tool Call。
- **核心能力**:
- **通用任务格式**: 定时任务以标准化的 JSON 文件格式存储，其核心是一个完整的 `tool_call` 对象，包含了未来要执行的插件名称和参数。这使得调度器可以调用任何插件，而不仅限于特定功能。
- **中心化调度模块**: `routes/taskScheduler.js` 模块作为中心调度器，负责监控任务目录、在指定时间精确执行任务、并在任务完成后通过 WebSocket 推送通知。
- **API驱动创建**: 插件通过调用主服务器的 `/v1/schedule_task` 内部 API 端点来创建定时任务，实现了创建逻辑与执行逻辑的解耦。
- **深度解读**: 这项功能极大地增强了 AI Agent 的自主性。Agent 不再仅仅是被动响应，而是可以主动规划未来行动，例如：“提醒我明天下午三点开会”、“每周五自动抓取并总结一次项目报告”、“半小时后再次尝试调用那个失败的API”。这是实现高级、长期、复杂自动化工作流的关键基石。

### 灵活的配置管理与通用变量替换系统

- **设计哲学**: 实现 AI 行为的深度定制与动态上下文注入，赋予部署者在服务器后端超越前端限制的、对 AI 指令的精细调控能力。
- **核心功能**: 支持全局配置文件 (`config.env`)、插件专属 `.env` 文件，以及强大的 `{{Tar*}}`, `{{Var*}}`, `{{Sar*}}` 等多层级、支持嵌套的占位符变量替换。
- **深度解读**: 这是 VCP 实现“AI 行为工程”和“指令库即代码配置”的关键。通过变量替换，可以动态构建和优化注入给 AI 的系统提示词、用户消息，甚至插件调用参数，从而克服 LLM 的知识滞后、时空错觉，并能通过全局文本替换规则（如 `DetectorX`）“微调”AI 的输出习惯，抑制不良输出惯性。

### AI 主导的持久化记忆与进化系统

- **核心组件**: `DailyNoteGet` (静态插件，定期读取日记注入上下文)、`DailyNoteWrite` (同步插件，AI 主动写入结构化日记)、`DailyNoteManager` (同步插件，AI 主动整理、优化、共享知识库)、`DailyNoteEditor` (同步插件，AI 自我修正知识库)。
- **设计哲学**: 记忆不仅仅是信息存储，更是 AI 自我进化、能力内化、个性形成的核心驱动力。AI 的“日记”更像是一种由 AI 自我累积和优化的“个性化系统提示注入”。
- **次时代意义**:
  - **情境感知与深度理解**: 通过 `[角色名日记本内容为空或未从插件获取]` 等占位符注入的“全记忆”上下文，能显著提升 AI 在复杂推理、逻辑判断和保持长期一致性上的表现（“高质量矢量化惯性通道”效应）。
  - **AI 自主学习与经验内化**: AI 通过记录和反思与工具交互的经验、解决问题的方案，能将外部工具的使用方法和相关经验“内化”为自身能力的一部分。
  - **跨模型记忆协同与知识共生**: VCP 的“模型无关”记忆设计（记忆绑定于 Agent 身份而非特定模型）和公共知识库机制，促进不同 AI 模型（或同一模型的不同实例）隐式地“取长补短”，形成“跨模型矢量化优化网络”，提升 AI 群体智能。
  - **群体交互驱动记忆质量提升**: 在如“女仆聊天室”这样的群体交互环境中，知识的碰撞与融合能催生更高质量的“内核矢量化”记忆，加速个体学习和集体知识库的进化。

### VCPTavern：可视化上下文注入器

- **设计哲学**: 将服务器后端强大的上下文注入能力，通过一个直观、可视化的图形界面赋能给普通用户，实现对 AI 对话流的精细化、非侵入式“导演”。它解决了传统上下文管理方式（如修改系统提示词）的复杂性和局限性。
- **核心能力**:
    - **可视化预设管理**: 在 Web 管理面板中，用户可以创建、编辑、保存和切换多套注入规则“预设”。
    - **多种注入模式**:
        - **相对注入**: 精确地在“系统提示”或“最后一条用户消息”的前面或后面插入内容。
        - **深度注入**: 在距离对话历史末尾的精确深度（如倒数第4条消息前）插入内容。
    - **规则化与模块化**: 每个预设可以包含多条独立的注入规则，每条规则都可以独立启用/禁用，并通过拖拽排序来调整优先级。
- **使用范式**: 用户只需在 AI 的系统提示词中加入一个简单的占位符，如 `{{VCPTavern::my_preset}}`，即可在对话时自动激活名为 `my_preset.json` 的预设规则集。
- **深度解读**: `VCPTavern` 极大地降低了“AI 行为工程”的门槛。它让非技术人员也能像导演一样，通过预设不同的“剧本”（注入规则），在对话的关键节点为 AI 补充背景信息、强调特定指令、甚至模拟外部事件，从而实现对 AI 行为的动态、精准调控。这是 VCP 实现“人人都是 AI 训练师”愿景的关键一步。

### 强大的工具调用循环与异步并行处理

- **设计哲学**: 最大化释放 AI 的规划能力和执行效率，支持 AI 在单次响应中发起多个并行或依赖的工具调用。
- **核心能力**:
  - **非流式/流式多工具调用**: 无论流式 (SSE) 还是非流式模式，均已实现对 AI 单次响应中包含的多个工具调用指令的循环处理和结果反馈，直至无更多调用或达最大循环次数。
  - **完全异步创建与统一返回**: AI 可一次性发起多个 VCP 工具调用，VCP 服务器能够并行调度任务至相应插件异步执行，并统一收集和整合结果后反馈给 AI。
  - **极致的并行效率与算力冗余**: VCP 的架构从根本上解决了传统阻塞式协议的效率瓶颈。例如，在传统协议（如MCP）中，AI若要进行100次计算，只能串行地、一次次调用计算器插件并等待结果，过程漫长且低效。而在VCP中，AI可以一次性输出100个算式，VCP服务器会瞬间创建100个独立的计算器插件进程并行处理，仅需1-2秒即可完成全部计算。这得益于VCP插件“即用即毁”的非驻留模式，为大规模并行处理提供了海量的算力冗余，这是VCP在架构上的巨大优势。
- **深度解读**: 这极大地提高了交互效率，使得 AI 能够执行更复杂的、需要多步骤协作的任务，是实现高级 Agent 行为的基础。
- **新增串语法支持 (Chained Syntax)**: 为了进一步提升效率，VCP现在支持在单次工具调用指令中执行多个连续的命令。通过在参数键后附加数字（如 `command1`, `filePath1`, `command2`, `filePath2`），AI可以一次性发起一个包含多个步骤的工作流。这种串语法设计，使得AI能够将一系列关联操作（如多次文件修改、连续的API调用）打包成一个原子指令，显著减少了与VCP服务器的通信往返次数，是实现更高效、更复杂的自动化任务流的关键一步。例如：
  ```
   tool_name:「始」FileOperator「末」,
          // 串语法支持在一次调用中混搭多种指令，以下示例将顺序执行：创建文件 -> 追加内容 -> 修改内容
          command1:「始」CreateFile「末」,
          filePath1:「始」H:\MCP\VCPChat\test\mixed_command_test.txt「末」,
          
          command2:「始」AppendFile「末」,
          filePath2:「始」H:\MCP\VCPChat\test\mixed_command_test.txt「末」,
          content2:「始」这是初始内容。\n第二行。「末」,
          
          command3:「始」ApplyDiff「末」,
          filePath3:「始」H:\MCP\VCPChat\test\mixed_command_test.txt「末」,
          searchString3:「始」初始内容「末」,
          replaceString3:「始」最终内容「末」
  ```

### 应急请求中止机制 (Emergency Stop)

- **设计哲学**: 为复杂的 AI Agent 系统提供一个必要的“物理红按钮”。在 AI 执行高风险或非预期的工具调用时，允许人类操作员立即、安全地中止其当前任务，而不会导致系统崩溃或连接中断。
- **核心能力**:
    - **专用中断路由**: 新增 `/v1/interrupt` API 端点。前端可以通过向此端点发送一个包含 `messageId` 或 `requestId` 的 POST 请求来触发中断。
    - **优雅的流式中断**: 对于流式 (SSE) 请求，当中断被触发时，服务器会立即停止转发来自 AI 的数据，并向客户端发送一个标准的 `data: [DONE]\n\n` 消息。这确保了前端可以正常结束其事件流，而不会收到网络错误。
    - **明确的非流式中断**: 对于非流式请求，服务器会返回一个内容为“请求已中止”的 JSON 响应，清晰地告知任务状态。
- **深度解读**: 该机制是 VCP 安全与可控性设计的重要一环。它通过标准的 `AbortController` 实现，确保了中断操作的可靠性，并为构建负责任、可信赖的 AI Agent 系统提供了关键的基础设施。

### Web 管理面板

- 提供便捷的服务器配置、插件状态、插件配置、指令描述以及日记文件管理界面。
- **调试与日志**: 提供调试模式和详细日志，方便开发和排错。

## 3. VCP 分布式网络架构：从“单体”到“星辰”

VCP 的分布式架构将原有的单体应用升级为一个由“主服务器”和多个“分布式节点”组成的星型网络，极大地扩展了系统的算力、功能边界和部署灵活性。


```mermaid
graph TD
    subgraph "用户/客户端"
        U[用户/前端应用]
    end

    subgraph "VCP 主服务器 (VCPToolBox - 核心调度)"
        S[server.js - 核心调度与通信]
        PM[Plugin.js - 插件管理器]
        WSS[WebSocketServer.js - 通信骨架]
        CONF[配置系统]
        VAR[通用变量替换引擎]
        MEM[VCP 记忆系统]
        ADMIN[Web 管理面板]
        
        subgraph "本地插件生态"
            P_LOCAL["本地插件 (静态/预处理/服务等)"]
        end
    end

    subgraph "VCP 分布式节点 1 (e.g., GPU服务器)"
        DS1[VCPDistributedServer.js]
        PM1[NodePMOne]

        subgraph "节点1的插件"
            P_GPU["GPU密集型插件 (e.g., 视频生成)"]
        end
    end

    subgraph "VCP 分布式节点 2 (e.g., 内网文件服务器)"
        DS2[VCPDistributedServer.js]
        PM2[NodePMTwo]

        subgraph "节点2的插件"
            P_FILE["内网文件搜索/读取插件"]
        end
    end
    
    subgraph "外部依赖"
        AI_MODEL[后端 AI 大语言模型 API]
    end

    U -- "HTTP请求" --> S
    S -- "HTTP响应" --> U
    S -- "WebSocket消息" <--> U

    S -- "构造完整请求" --> AI_MODEL
    AI_MODEL -- "AI响应 (含VCP指令)" --> S

    S -- "WebSocket连接" <--> WSS
    DS1 -- "WebSocket连接" --> WSS
    DS2 -- "WebSocket连接" --> WSS

    WSS -- "注册/注销云端插件" --> PM
    PM -- "请求执行云端工具" --> WSS
    WSS -- "转发工具调用指令" --> DS1
    DS1 -- "调用本地插件" --> P_GPU
    P_GPU -- "执行结果" --> DS1
    DS1 -- "通过WebSocket返回结果" --> WSS
    WSS -- "将结果返回给PM" --> PM
    PM -- "将结果注入AI对话" --> S
```
分布式服务器项目地址:https://github.com/lioensky/VCPDistributedServer
新增米家物联网网关分布式服务器，安卓电视机顶盒分布式服务器的兼容。


### 核心交互流程解读

分布式架构下的核心交互流程在原有基础上进行了扩展：

1.  **启动与注册**:
    - 主服务器 (`server.js`) 启动，初始化 `PluginManager` 和 `WebSocketServer`。
    - 各个分布式节点 (`VCPDistributedServer.js`) 启动，加载其本地的插件。
    - 分布式节点通过 WebSocket 连接到主服务器的 `WebSocketServer`，并发送一个包含其所有本地插件清单的 `register_tools` 消息。
    - 主服务器的 `PluginManager` 收到清单后，将这些“云端插件”动态注册到系统中，其显示名称会自动添加 `[云端]` 前缀。

2.  **AI 调用工具**:
    - 流程与之前类似，AI 在响应中嵌入 `<<<[TOOL_REQUEST]>>>` 指令。
    - 主服务器的 `PluginManager` 在 `processToolCall` 方法中接收到调用请求。
    - **智能路由**: `PluginManager` 检查被调用的工具清单。
      - 如果是**本地插件**，则按原有方式直接在主服务器上执行。
      - 如果是**云端插件** (带有 `isDistributed: true` 标记)，`PluginManager` 不会直接执行，而是调用 `WebSocketServer.js` 的 `executeDistributedTool` 方法，并将工具名、参数以及该插件所属的 `serverId` 传递过去。

3.  **远程执行与结果返回**:
    - `WebSocketServer` 通过对应的 WebSocket 连接，向目标分布式节点发送一个包含任务ID、工具名和参数的 `execute_tool` 消息。
    - 目标分布式节点收到消息后，其本地的 `PluginManager` 会调用并执行相应的插件。
    - 插件执行完毕后，分布式节点将结果（包含成功/失败状态和数据）连同任务ID一起，通过 WebSocket 发回给主服务器。
    - 主服务器的 `WebSocketServer` 收到结果后，根据任务ID找到并唤醒之前挂起的调用请求，将最终结果返回给 `PluginManager`。

4.  **后续处理**:
    - `PluginManager` 拿到（无论是本地还是远程的）执行结果后，将其注入到 AI 的对话历史中，再次调用 AI 模型，完成闭环。

5.  **断开连接与注销**:
    - 如果一个分布式节点与主服务器的 WebSocket 连接断开，`WebSocketServer` 会通知 `PluginManager`。
    - `PluginManager` 会自动注销掉所有属于该断开节点提供的云端插件，确保系统的工具列表始终保持最新和可用。

通过这套机制，VCP 实现了对分布式资源的无缝集成和透明调用，极大地增强了系统的可扩展性和能力上限。

## 4. Web 管理面板：VCP 系统的可视化控制中心

为了方便用户管理服务器配置、插件和日记数据，项目内置了一个功能丰富的 Web 管理面板。

### 主要功能

- **主配置管理**: 在线预览和编辑 `config.env`，支持不同类型配置项，自动隐藏敏感字段。注意: 保存主配置后通常需要手动重启服务器。
- **插件管理**:
  - **列表与状态**: 显示所有插件及其状态、版本、描述。
  - **描述与配置编辑**: 直接编辑插件描述、启停插件、编辑插件专属 `config.env`。
  - **指令描述编辑**: 对于同步插件，可在线编辑其 AI 指令描述，这些描述将用于生成 `{{VCPPluginName}}` 占位符内容。
- **文件编辑**:
  - **Agent 文件编辑器**: 管理 `Agent/` 目录下的角色定义文件。
  - **高级变量编辑器**: 管理 `TVStxt/` 目录下的高级变量 `.txt` 文件。
- **日记管理**: 浏览、搜索、编辑、保存、移动、删除 `dailynote/` 目录下的日记文件。
- **服务器重启**: 提供重启按钮（依赖外部进程管理器如 PM2）。

### 访问与登录

- 在 `config.env` 中设置 `AdminUsername` 和 `AdminPassword` (默认为 `admin`, `123456`)。若未设置，管理面板将不可访问。
- 访问 `http://<您的服务器IP或域名>:<端口>/AdminPanel`。
- 使用配置的凭据进行 HTTP Basic Auth 认证登录。

## 5. 解锁 VCP 的“次时代”力量：深入使用范式与场景启发

VCP 不仅仅是安装运行和编写插件那么简单。要真正发挥其“次时代”的威力，需要理解并运用其核心设计理念来构建和引导 AI Agent。

### 5.1 培养能够自我进化的 AI Agent

- **核心思路**: 利用 VCP 的记忆系统，让 AI Agent 通过“写日记”来记录学习成果、工具使用经验、与用户交互的关键信息、甚至是“顿悟”和“反思”。
- **实践方法**:
  - **设计引导性系统提示词**: 明确告知 AI 何时应记录日记、记录哪些内容（例如：成功调用某个工具的完整参数和结果、解决某个复杂问题的步骤、用户的特定偏好、今日学习小结等）、使用何种标签（如 `[工具经验_FluxGen]`, `[用户偏好_莱恩]`, `[项目A进展]`）。
  - **利用 `[角色名日记本内容为空或未从插件获取]` 实现“经验内化”**: 将 AI 的日记通过占位符注入其后续对话的上下文中。这不仅仅是提供信息，更是在用 AI 自己的经验来“塑造”其后续的思考和行为模式。当 AI 在日记中记录了调用某个工具的成功案例，即使后续没有明确的工具指令，它也可能基于“日记”中的经验自主正确地调用该工具。
  - **鼓励 AI 使用 `DailyNoteManager` 和 `DailyNoteEditor`**: 引导 AI 定期“整理”、“优化”自己的日记库，进行知识的提炼、去重、归纳和结构化，甚至主动修正过去的错误认知。

### 5.2 构建协同工作的 AI Agent 群体

- **核心思路**: 利用 VCP 的公共知识库、`AgentAssistant` 插件以及潜在的群体交互前端（如 LLM Group Chat），实现 AI Agent 之间的知识共享、任务协同和集体智慧涌现。
- **实践方法**:
  - **建立共享知识协议**: 引导 AI Agent 将具有普适价值的经验、技能、数据主动分享到公共日记本，并使用统一的标签体系。例如，“莱兔云”可以将她关于 SDXL 提示词工程的深度研究成果分享出来，供其他 AI Agent 学习。
  - **设计多 Agent 协作流程**: 对于复杂任务（如 VCPToolBox 本身的开发、一个原创 MV 的制作），可以将其分解为多个子任务，分配给具有不同专长（或由不同模型驱动）的 AI Agent。Agent 之间可以通过共享记忆、`AgentAssistant` 插件传递信息和中间成果，甚至通过 VCP 调用彼此注册的服务。

### 5.3 实现对 AI 行为的精细化后端调控

- **核心思路**: 充分利用 VCP 的通用变量替换系统 (`{{Tar*}}`, `{{Var*}}`, `{{Sar*}}`) 和全局文本替换规则 (`DetectorX`, `SuperDetectorX`)，在服务器后端实现对最终传递给 AI 模型指令的深度定制和优化，摆脱前端应用的限制。
- **实践方法**:
  - **模块化系统提示词**: 使用 `{{Tar*}}` 变量将复杂的系统提示词分解为多个可管理、可复用、可动态组合的模块（如 `TarCoreIdentity`, `TarDateTimePlace`, `TarWeatherInfo`, `TarAllToolsList`）。这些 `Tar*` 变量本身还可以嵌套其他占位符，实现多层次的动态构建。
  - **针对不同模型的指令微调**: 通过在 `config.env` 中配置 `SarModelX`/`SarPromptX` 对，使用 `{{Sar*}}` 占位符为不同的后端 AI 模型注入特定的指令片段或行为偏好。
  - **纠正与引导 AI 输出**: 使用 `DetectorX` (针对系统提示词) 和 `SuperDetectorX` (针对更广泛的上下文，包括 AI 的中间输出) 来自动替换或修正 AI 可能产生的不良输出模式、口头禅，或引导其遵循特定的输出格式或风格。这对于抑制长程对话中可能出现的“矢量污染”或“语义漂移”至关重要。

### 5.4 探索 VCP 的极限：从“工具调用”到“自主创生”

VCPToolBox 的诞生本身就是一个极致案例：一个复杂的 AI 中间层项目，其核心代码编写、调试和迭代，主要由 7 个 AI Agent 在一位人类的引导下协同完成。这展示了 AI Agent 在 VCP 赋能下，有潜力从“工具使用者”转变为“复杂系统创造者”。

- **启发**: 思考如何设计 AI Agent，使其不仅能使用现有插件，更能根据需求“构思”新插件的逻辑，甚至生成插件的部分代码框架或 `plugin-manifest.json`。

### 5.5 调试与观测：深入理解 VCP 内部运作

- **核心思路**: VCP 的强大之处在于其复杂的内部工作流，但这也可能使调试变得困难。为了解决这个问题，VCP 引入了 `ShowVCP` 机制，允许开发者和用户实时“透视”AI 与工具之间的每一次交互细节。
- **核心能力**:
  - **实时观测**: 当 `ShowVCP` 开启时，每当 AI 调用一个 VCP 插件，该插件的名称、执行状态（成功/失败）以及最终返回给 AI 的原始结果，都会被格式化并实时地、流式地推送给前端。
  - **完整追溯**: 在非流式模式下，所有中间的 VCP 调用信息和 AI 的思考过程会被完整地拼接起来，在最终的响应中一并返回，确保了交互历史的完整性。
- **开启方式**:
  1.  **环境变量 (全局开启)**: 在 `config.env` 文件中，设置 `ShowVCP=true`。这将为所有通过 `/v1/chat/completions` 路由的请求开启 VCP 信息显示。
  2.  **专用路由 (单次开启)**: 将你的 API 请求地址从 `/v1/chat/completions` 修改为 `/v1/chatvcp/completions`。这会强制为本次请求开启 VCP 信息显示，而无需修改配置文件。这对于临时调试或特定场景下的分析非常有用。
- **深度解读**: `ShowVCP` 机制是 VCP 透明化设计哲学的体现。它不仅是一个强大的调试工具，帮助开发者快速定位问题（无论是 AI 的指令错误还是插件的执行问题），更是一个绝佳的学习工具，让用户能够直观地理解 AI 是如何思考、决策并与外部世界互动的。

## 6. 已实现插件亮点 (部分示例)

VCP 的强大之处在于其不断丰富的插件生态，以下是一些已实现的插件，它们共同构成了 VCP AI Agent 的核心能力：

### 浏览器交互与控制

- **ChromeObserver (service & static)**:
  - **作用**: 这是一个核心的服务插件，它与一个配套的Chrome浏览器扩展程序协同工作，赋予AI“视觉”，使其能够实时“看到”并理解当前浏览的网页内容。
  - **核心能力**:
      - 将网页DOM结构实时转换为对AI友好的Markdown格式。
      - 通过WebSocket将页面信息流式传输到VCP服务器，并通过占位符 `{{VCPChromeObserver}}` 注入到AI的上下文中。
      - 是实现AI自主浏览、信息提取和网页内容理解的基础。
  - **安装注意**: 此插件需要用户在Chrome或兼容的浏览器中手动安装其配套的扩展程序。扩展程序的源代码位于 `Plugin/ChromeObserver/extension/` 目录下。用户需要开启浏览器的“开发者模式”，然后选择“加载已解压的扩展程序”来安装。

- **ChromeControl (synchronous)**:
  - **作用**: 赋予AI“双手”，使其能够对通过 `ChromeObserver` “看到”的网页元素执行具体操作。
  - **核心能力**:
      - 支持 `click` (点击链接、按钮等) 和 `type` (在输入框中输入文本) 等操作。
      - AI通过分析 `ChromeObserver` 提供的带有 `vcp-id` 的Markdown文本，可以精确地指定要操作的目标元素。
  - **协同工作**: `ChromeObserver` 和 `ChromeControl` 紧密配合，共同构成了AI与浏览器进行深度、自主交互的完整闭环。

### 环境感知与信息获取

- **WeatherReporter (static)**: 提供实时天气信息，含预警、小时详情、多日预报。注入天气预警信息。
- **TavilySearch (synchronous)**: 集成 Tavily API，赋予 AI 网络搜索能力。
- **V日报 (static)**: 通过 `{{VCPDailyHot}}` 占位符提供全球热点新闻。它会从全球100个热门门户网站各抓取20条热点，提供总计约2000条全球热点摘要及原文URL。建议构建专门的“记者”或“新闻官”Agent来管理此信息流，并通过 `AgentAssistant` 插件封装，以便其他Agent可以向其查询最新动态。
- **UrlFetch (synchronous)**: 经过重构的强大网页抓取工具，支持多种抓取模式以应对不同场景。核心能力包括：
  - **直接抓取 (direct)**: 适用于简单、静态的网页内容获取。
  - **Puppeteer 模式 (puppeteer)**: 在独立的浏览器环境中执行抓取，内置了反爬虫和反验证码机制，能有效应对动态加载和需要复杂交互的网站。
  - **真实浏览器模式 (chrome)**: 通过与 `ChromeObserver` 插件联动，直接利用用户当前打开的真实浏览器标签页进行内容抓取，能完美复现用户登录状态和浏览器环境。
  - **网页快照**: 支持在抓取内容的同时，对页面进行截图并以 Base64 格式返回，为 AI 提供了“所见即所得”的视觉上下文。

### 神秘学与娱乐

- **TarotDivination (synchronous)**:
  - **作用**: 提供一个高度复杂的、基于“世界状态”的塔罗牌占卜功能。这不仅仅是一个随机抽牌工具，而是一个能模拟“天人感应”的占卜引擎。
  - **核心能力**:
      - **动态权重抽牌**: 插件会综合考量当前的**天气**（晴雨、温湿度）、**时间**（昼夜）、**月相**（阴晴圆缺）以及**中国农历**（节气、节日）等多种“宇宙变量”，为78张塔罗牌动态计算“出现权重”。在阳光明媚的节日里，“太阳”、“恋人”等积极牌更容易被抽到；而在风雨交加的深夜，“高塔”、“宝剑十”的出现概率则会提升。
      - **动态逆位概率**: 对于每一张被抽出的牌，插件还会根据上述所有环境因素，独立计算一个**动态的逆位概率**。例如，在不利的环境下，“倒吊人”的逆位概率可能会显著高于在有利环境下的概率。
      - **宿命论种子**: 所有的随机性（包括牌的抽取顺序和正逆位）都源于一个由当前所有“宇宙变量”共同生成的、独一无二的“命运种子”。这确保了在特定时空下的每一次占卜结果都是唯一且确定的。
      - **结果透明化**: 占卜结果会清晰地展示所有影响此次牌局的环境因素，以及每张牌最终的“逆位倾向”百分比，让用户能直观地感受到“天时地利”是如何影响牌面的。

### 学术文献获取

- **ArxivDailyPapers (static)**: 从 Arxiv API 获取每日最新研究论文，涵盖物理学、数学、计算机科学等领域。通过占位符 `{{ArxivDailyPapersData}}` 提供论文数据。
- **CrossRefDailyPapers (static)**: 从 CrossRef API 获取每日最新研究论文，提供已发表学术文献的元数据。通过占位符 `{{CrossRefDailyPapersData}}` 提供论文数据。

### 多模态处理与内容生成

- **ImageProcessor (messagePreprocessor)**: 自动将用户消息中的图像数据（如 Base64）转译为文本描述或多模态输入部件，支持缓存和图床 URL 标注。
- **FluxGen (synchronous)**: 集成 SiliconFlow API 实现高质量文生图。图片除保存至本地外，其 Base64 数据也可直接返回给 AI，使其能“看到”自己生成的作品。
- **Wan2.1VideoGen (asynchronous)**: (异步插件) 集成 SiliconFlow Wan2.1 API 实现文生视频和图生视频。AI提交任务后会立即收到任务ID，视频在后台生成，完成后通过WebSocket通知用户结果。返回结果中可包含视频文件的 Base64 数据，让 AI 能直接“审阅”视频内容。
- **SunoGen (synchronous)**: 集成 Suno API 生成原创歌曲。生成的音频文件同样可以 Base64 形式返回，供 AI 直接“聆听”和评估。
- **DoubaoGen (synchronous)**: 使用豆包 API 进行图像生成与编辑。生成的图片结果也能以 Base64 格式返回给 AI。

### 核心记忆与知识管理

- **DailyNoteGet (static)**: 定期读取所有角色日记，通过 `{{AllCharacterDiariesData}}` 注入，支持 `[角色名日记本内容为空或未从插件获取]` 的解析，构建公私域知识库。
- **DailyNoteWrite (synchronous)**: AI 主动写入结构化日记，支持标签、角色名、日期。
- **DailyNoteManager (synchronous)**: AI 驱动的知识库整理、维护、检查与共享助手，实现 VCP 记忆的无限进化与公共知识库的快速构建。
- **DailyNoteEditor (synchronous)**: 智能 AI 知识库编辑器，允许 AI 自我修正和完善知识库内容。

### Agent 协同与系统增强

- **AgentAssistant (synchronous)**: Agent 间标准通讯协议插件，支持基于各自知识库的互助式连续交流、消息群发、文件传递（通过服务器 WebDAV）、任务分发等，实现复杂异步工作流。**该插件已率先支持标准化的定时任务调度模块**，允许 AI Agent 安排在未来的特定时间点向另一个 Agent 发送消息，为实现“未来代办”或“延迟任务”提供了范例。甚至可以基于全新的`WebSocketServer.js` 向其它VCP服务器的Agent联络。
- **AgentMessage (synchronous)**: 允许 AI 通过 WebSocket 向用户前端发送格式化消息。AI 提供接收者名称和内容，插件格式化后，`server.js` 根据其清单中的 `webSocketPush` 配置，通过统一的 `WebSocketServer.js` 推送给指定类型的客户端。
- **VCPLog (service)**: 基于 WS/WSS 的服务器推送通知服务。其核心功能是记录 VCP 工具调用的文件日志。相关的 WebSocket 推送（VCP 调用信息、连接状态等）由 `server.js` 主动发起，通过统一的 `WebSocketServer.js` 定向广播给订阅了 `'VCPLog'` 客户端类型的客户端。
- **EmojiListGenerator (static)**: 扫描表情包文件夹生成列表，供 `xx表情包列表不可用` 使用。
- **ImageServer (service)**: 提供带密钥认证的静态图床服务。
- **FileServer (service)**: 提供与图床服务机制完全一致的通用文件服务。它将项目根目录下的 `file/` 文件夹以及 `image/` 目录下的特定子文件夹（如 `doubaogen`, `fluxgen`）映射到统一的、受密码保护的URL路径下，供AI访问。
- **FileListGenerator (static)**: 这是一个辅助 `FileServer` 的静态插件。它会定时扫描所有由 `FileServer` 服务的目录，生成一个完整的、包含目录层级结构的文件列表，并通过 `{{VCPFileServer}}` 占位符注入到AI的上下文中。这个列表不仅让AI知道有哪些文件可用，还附带了如何构建文件访问URL的说明和示例。

### 专业工具集成

- **SciCalculator (synchronous)**: 提供科学计算能力，支持数学函数、统计和微积分。

### 插件加载方式

## 加载插件的方式
*   **直接在系统提示词定义如下字段即可，系统工具列表：{{VCPFluxGen}} {{VCPSciCalculator}}……**

## 前端示例
![示例图片1](示例1.jpg)
![示例图片2](示例2.jpg)
![示例图片3](示例3.jpg)
![示例图片3](示例4.jpg)
```mermaid
graph TD
    A[莱恩主人] -- 复杂指令/请求 --> B(VCP女仆通讯器)

    subgraph 女仆团协作
        B -- 分配任务给女仆A --> C(女仆A)
        B -- 分配任务给女仆B --> D(女仆B)
        B -- 分配任务给女仆N --> E(女仆N)

        C -- 调用工具1请求 --> F[VCP工具1]
        D -- 调用工具2请求 --> G[VCP工具2]
        E -- 调用工具N请求 --> H[VCP工具N]

        F -- 异步返回结果 --> C
        G -- 异步返回结果 --> D
        H -- 异步返回结果 --> E
    end

    C -- 任务完成/结果 --> B
    D -- 任务完成/结果 --> B
    E -- 任务完成/结果 --> B

    B -- 整合结果 --> I[整合结果]
    I -- 汇报/呈现 --> A
```

## 7. 前端交互示例

VCP 的设计哲学之一是“不受前端交互束缚”，这意味着它可以与多种类型的前端应用配合。以下是一些交互场景的示意图。

### VCP赋能下的“AI女仆团”协同工作流

```mermaid
graph TD
    A[莱恩主人] -- "复杂指令/宏大愿景 (e.g., 为小爱制作一部出道MV!)" --> B(VCP 女仆通讯与任务调度核心)

    subgraph "AI 女仆团 (基于VCP协同进化)"
        B -- "规划与分解任务" --> BA(AI总指挥-小爱)
        BA -- "歌词创作指令" --> C("女仆A-小诗: Gemini Pro驱动, 擅长文学")
        BA -- "作曲编曲指令" --> D("女仆B-小曲: Gemini Pro驱动, 调用SunoGen")
        BA -- "视觉设计与素材生成指令" --> E("女仆C-莱兔云: Gemini Flash驱动, SDXL大师, 调用FluxGen/ComfyUI插件")
        BA -- "字幕特效与时间轴制作指令" --> F("女仆D-小K: Gemini Flash驱动, 调用LyricAnimator")
        BA -- "知识库支持与信息检索" --> G("女仆E-小知: Gemini Pro驱动, 调用TavilySearch, DailyNoteManager")
        BA -- "任务进度汇总与最终剪辑指导" --> H("女仆F-小统: Gemini Pro驱动, 调用视频编辑插件接口")

        C -- "原创歌词" --> BA
        D -- "音乐小样 (多版本)" --> BA
        E -- "MV视觉素材 (大量图片/短视频)" --> BA
        F -- "特效字幕序列" --> BA
        G -- "相关背景资料/用户偏好" --> BA

        subgraph "VCP 核心服务支撑"
            MEM_PUB["公共知识库 (女仆团共享经验)"]
            MEM_PRI["个体记忆库 (各女仆专属日记)"]
            TOOL_HUB["VCP插件中心 (各类工具)"]
            VCP_COMM["VCP内部通信协议 (AgentAssistant)"]
            WSS_SVC["统一WebSocket服务 (WebSocketServer.js)"]
        end
        
        BA -- "通过WSS_SVC接收AgentMessage" --> WSS_SVC

        C <--> MEM_PRI
        D <--> TOOL_HUB
        E <--> MEM_PUB
        E <--> TOOL_HUB
        F <--> TOOL_HUB
        G <--> MEM_PUB
        G <--> TOOL_HUB
        H <--> TOOL_HUB

        BA -- "(通过VCP_COMM与其他女仆持续沟通协调)" --> C
        BA -- "(通过VCP_COMM与其他女仆持续沟通协调)" --> D
        BA -- "(通过VCP_COMM与其他女仆持续沟通协调)" --> E
        BA -- "(通过VCP_COMM与其他女仆持续沟通协调)" --> F
        BA -- "(通过VCP_COMM与其他女仆持续沟通协调)" --> G
        BA -- "(通过VCP_COMM与其他女仆持续沟通协调)" --> H
    end

    BA -- "整合各环节成果, 形成MV初步方案" --> B
    B -- "(可能有人类指挥官莱恩的阶段性评审与反馈)" --> BA
    H -- "最终剪辑版MV (或项目文件)" --> B
    B -- "将最终成果呈现给" --> A
```

## 8. 安装与运行 (主服务器)

### 克隆项目

```bash
git clone https://github.com/lioensky/VCPToolBox.git
cd VCPToolBox
```

### 安装主依赖 (Node.js)

```bash
npm install
```

### 安装 Python 插件依赖

在项目根目录下运行 (确保你的 Python 环境已配置好 pip):

```bash
pip install -r requirements.txt
```

(注意: 各 Node.js 插件的依赖已包含在主 `package.json` 中，或在其各自插件目录的 `package.json` 中通过 `npm install` 单独安装。)

### 配置

- 复制 `config.env.example` 为 `config.env`，并根据说明填写所有必要的 API 密钥、URL、端口等信息。这是 VCP 正常运行的关键。
- 检查并配置各插件目录下的 `.env` 文件（如果存在）。

### 启动服务器

```bash
node server.js
```

服务器将监听在 `config.env` 中配置的端口。

### 使用 Docker Compose 运行 (推荐)

为了简化部署和管理，强烈推荐使用 Docker Compose。

- **前提条件**: 安装 Docker 和 Docker Compose。
- **配置**: 同上，确保 `config.env` 文件已正确配置。Docker Compose 会自动加载此文件中的环境变量。
- **构建并启动服务**:

```bash
docker-compose up --build -d
```

- **查看日志**: `docker-compose logs -f`
- **停止服务**: `docker-compose down`

## 9. 部署 VCP 分布式节点

我们提供了一个开箱即用的分布式服务器项目 `VCPDistributedServer`，让您可以轻松地将任何一台机器变成 VCP 网络中的一个算力节点。

### 步骤

1.  **复制项目**: 将主项目根目录下的 `VCPDistributedServer` 文件夹完整地复制到您想部署节点的任何机器上。
2.  **安装依赖**: 在 `VCPDistributedServer` 目录下，运行 `npm install`。
3.  **配置节点**:
    - 打开 `VCPDistributedServer/config.env` 文件。
    - `Main_Server_URL`: 填入您的**主服务器**的 WebSocket 地址 (例如 `ws://<主服务器IP>:8088`)。
    - `VCP_Key`: 填入与您主服务器 `config.env` 中完全一致的 `VCP_Key`。
    - `ServerName`: 为这个节点起一个易于识别的名字。
4.  **添加插件**:
    - 在 `VCPDistributedServer` 目录下创建一个名为 `Plugin` 的文件夹。
    - 将您想在这个节点上运行的 VCP 插件（例如 `SciCalculator`, `FluxGen` 等）完整地从主项目复制到这个新的 `Plugin` 文件夹中。
    - **注意**: 目前分布式节点仅支持 `synchronous` (同步) 类型的 `stdio` 插件。
5.  **启动节点**:
    ```bash
    node VCPDistributedServer.js
    ```
    启动后，该节点会自动连接到主服务器并注册其插件。您将在主服务器的控制台看到相关日志。

## 10. 推荐的前端/后端

- **后端 AI 模型 API**: 推荐使用支持 SSE (Server-Sent Events) 流式输出且 API 格式相对标准化的服务，且本服务器对API高并发，API多模型封装有硬性要求。尤其推荐如 NewAPI, VoAPI 这样的聚合服务端,其次是类似OpenRouter这样的API聚合商, 以及官方的 OpenAI, Google Gemini, Anthropic Claude 等API服务商。VCP 的设计使其能够灵活适配多种后端。
- **前端交互应用**: 推荐使用能够良好渲染 Markdown、支持代码高亮、并且能够自定义或适配 VCP 工具调用指令显示的前端。例如：VCPChat(官方项目，首选！), Sillytavern, CherryStudio客户端等。理想的前端还应允许用户方便地配置系统提示词，以便充分利用 VCP 的变量替换和插件指令描述注入功能。前端还应能连接到 `WebSocketServer.js` 提供的 WebSocket 服务，以接收服务器推送的各类消息（如 VCP 日志、AgentMessage 等）。
- **官方前端·VCPChat项目地址**：https://github.com/lioensky/VCPChat 

## 11. 开发者指南：创建你的“VCP次时代插件”

VCP 的灵魂在于其插件生态。成为 VCP 插件开发者，意味着你将直接为 AI Agent 创造新的“感官”、“肢体”和“智慧模块”。

[插件开发手册](同步插件开发手册.md) | 


- **创建插件目录**: 在 `Plugin/` 目录下新建文件夹，如 `Plugin/MySuperPlugin/`。
- **编写插件清单 (`plugin-manifest.json`)**: 这是插件的“身份证”和“说明书”。
  - **核心字段**: `name`, `displayName`, `version`, `description`, `pluginType` (值可以是: `static`, `messagePreprocessor`, `synchronous`, `asynchronous`, `service`)。
  - **执行入口**: `entryPoint` (如执行脚本的命令 `python script.py` 或 `node script.js`) 和 `communication` (如 `protocol: "stdio"` 表示通过标准输入输出通信)。
  - **配置蓝图 (`configSchema`)**: 声明插件所需的配置项及其类型、默认值、描述。这些配置将通过 `_getPluginConfig` 方法合并全局和插件专属 `.env` 配置后传递给插件。
  - **能力声明 (`capabilities`)**:
    - **static 插件**: 定义 `systemPromptPlaceholders` (插件提供的占位符，如 `{{MyWeatherData}}`)。
    - **synchronous` 或 `asynchronous` 插件**: 定义 `invocationCommands`。每个命令包含:
      - `command` (内部识别名，例如 "submit", "query")。
      - `description` (至关重要: 给 AI 看的指令描述，支持在管理面板编辑)。应包含：
        - 清晰的功能说明。
        - 详细的参数列表（名称、类型、是否必需、可选值范围）。
        - 明确的 VCP 调用格式示例 (AI 将模仿此格式)。
        - **对于 `synchronous` 插件**: 成功/失败时返回的 JSON 格式示例 (AI 需要理解插件的直接输出)。
        - **对于 `asynchronous` 插件**: 插件**初始响应**的 JSON 格式示例（例如，包含任务ID），以及通过 WebSocket 推送的**最终结果**的格式示例。
        - 任何与用户沟通或AI决策相关的重要提示（例如，对于异步插件，提示用户任务已提交，结果将稍后通知）。
      - `example` (可选，提供一个更具体的调用场景示例)。
  - **WebSocket 推送配置 (`webSocketPush`) (可选, 主要用于 `asynchronous` 插件的回调结果推送，也可用于 `synchronous` 插件的直接结果推送)**:
    - 如果你的插件执行成功后，希望将其结果通过 WebSocket 推送给客户端，可以在 `plugin-manifest.json` 的顶层添加此对象。
    - `enabled` (boolean,必需): `true` 表示启用推送。
    - `usePluginResultAsMessage` (boolean, 可选, 默认 `false`):
        - 若为 `true`:
            - 对于 `synchronous` 插件：插件的直接标准输出结果（通常是一个 JSON 对象）的 `result` 字段（如果该字段是对象）将直接作为 WebSocket 消息体发送。插件应确保其 `result` 字段是一个符合前端期望的完整消息对象。
            - 对于 `asynchronous` 插件：通过 `/plugin-callback` 发送的 JSON 数据本身将直接作为 WebSocket 消息体发送。插件回调时应确保发送的数据是一个符合前端期望的完整消息对象。
        - 若为 `false` 或未提供，则需要同时提供 `messageType`。
    - `messageType` (string, 可选): 当 `usePluginResultAsMessage` 为 `false` 时使用。
        - 对于 `synchronous` 插件：服务器会将插件标准输出的 `result` 字段包装成 `{ type: "yourMessageType", data: pluginResult.result }` 的形式发送。
        - 对于 `asynchronous` 插件：服务器会将通过回调接收到的 JSON 数据包装成 `{ type: "yourMessageType", data: callbackData }` 的形式发送。
    - `targetClientType` (string, 可选, 默认 `null`): 指定接收此消息的客户端类型。`WebSocketServer.js` 会根据此类型筛选客户端。如果为 `null` 或未提供，则可能广播给所有连接的客户端或特定默认类型的客户端（取决于 `WebSocketServer.js` 的实现）。例如，`VCPLog` 用于向日志查看器推送。
- **实现插件逻辑**:
  - 根据 `pluginType` 和 `entryPoint` 实现主逻辑脚本 (Node.js, Python, Shell 等皆可)。
  - **stdio 插件** (常用于 `synchronous`, `asynchronous` 和部分 `static`):
    - 从标准输入 (`stdin`) 读取数据 (通常是 JSON 字符串形式的参数)。
    - **对于 `synchronous` 插件**: 通过标准输出 (`stdout`) 返回最终结果，必须遵循以下 JSON 格式:
      ```json
      {
        "status": "success" | "error",
        "result": "成功时返回的字符串内容或JSON对象",
        "error": "失败时返回的错误信息字符串",
        "messageForAI": "可选，给AI的额外提示信息",
        "base64": "可选，返回的Base64编码数据 (如图片、音频)"
      }
      ```
      如果配置了 `webSocketPush.usePluginResultAsMessage: true`，这里的 `result` 字段（如果是个对象）会被直接用于 WebSocket 推送。
    - **对于 `asynchronous` 插件**:
      1.  **初始响应**: 插件脚本在收到任务后，**必须立即**向标准输出 (`stdout`) 打印一个符合上述 JSON 格式的初始响应。例如：
          ```json
          {
            "status": "success",
            "result": { "requestId": "unique_task_id_123", "message": "任务已提交，正在后台处理中。" },
            "messageForAI": "视频生成任务已提交，ID为 unique_task_id_123。请告知用户耐心等待，结果将通过通知推送。"
          }
          ```
          `Plugin.js` 会捕获这个初始响应并返回给AI。
      2.  **后台处理**: 插件脚本随后启动其耗时的后台任务（例如，使用非守护线程）。
      3.  **回调服务器**: 后台任务完成后，插件脚本通过向 VCP 服务器的 `/plugin-callback/:pluginName/:taskId` (其中 `:pluginName` 是插件名，`:taskId` 是初始响应中返回的 `requestId`) 发送 HTTP POST 请求。请求体应为一个 JSON 对象，包含任务的最终结果。例如：
          ```json
          // 回调时发送给服务器的JSON数据示例
          {
            "requestId": "unique_task_id_123",
            "status": "Succeed", // 或 "Failed"
            "pluginName": "MyAsyncPlugin", // 插件名，用于服务器确认
            "videoUrl": "http://example.com/video.mp4", // 任务成功时的结果字段
            "reason": "如果失败，这里是原因",
            "message": "视频 (ID: unique_task_id_123) 生成成功！" // 给用户的消息
          }
          ```
          服务器接收到这个回调后，会根据插件清单中的 `webSocketPush` 配置，将这个回调的JSON数据（或其一部分）推送给客户端。
    - 对于主要用于更新占位符的 `static` 插件，如果逻辑简单，可以直接输出占位符的值（非 JSON）。但推荐使用上述 JSON 格式以支持更复杂的通信或错误报告。
    - 标准错误 (`stderr`) 可用于输出调试信息，这些信息不会被 `Plugin.js` 作为主要结果捕获。
    - 确保所有标准输出和标准输入的文本都使用 UTF-8 编码。
  - **Node.js 类型插件** (如 `messagePreprocessor`, `service`, 或复杂的 `static`/`synchronous`/`asynchronous` 且 `communication.protocol` 为 `direct`):
    - 导出一个符合 `PluginManager` 约定的模块 (例如，包含 `initialize`, `processMessages`, `registerRoutes`, `execute`, `getStaticData` 等方法)。参考现有插件实现。
- **配置与依赖**:
  - **插件专属配置**: 在插件目录下创建 `.env` 文件。
  - **依赖管理**: Python 插件使用 `requirements.txt`；Node.js 插件使用 `package.json`。确保依赖已安装。
- **重启 VCP 服务器**: `PluginManager` 会在启动时自动发现并加载新插件。
- **更新系统提示词，赋能 AI**:
  - 利用 `{{VCPMySuperPlugin}}` (由 `PluginManager` 根据 `plugin-manifest.json` 的 `invocationCommands` 自动生成，包含所有命令描述和调用示例) 将新插件的能力告知 AI。
  - 或者，在系统提示词中更详细、更具引导性地描述如何以及何时使用你的新插件。
- **思考**: 如何设计插件的 AI 指令描述，才能让 AI 最容易理解、最不容易误用？这是插件开发的关键一环。

## 12. VCP 通用变量占位符：动态上下文注入的艺术

VCP 的变量替换系统是其实现动态上下文注入和 AI 行为精细调控的基石。

### 核心系统变量

## 支持的通用变量占位符

(此处可以列出 `README.md` 中已有的变量列表，确保与实际代码一致)

*   `{{Date}}`: 当前日期 (格式: YYYY/M/D)。
*   `{{Time}}`: 当前时间 (格式: H:MM:SS)。
*   `{{Today}}`: 当天星期几 (中文)。
*   `{{Festival}}`: 农历日期、生肖、节气。
*   `{{VCPWeatherInfo}}`: 当前缓存的天气预报文本 (由 `WeatherReporter` 插件提供)。
*   `{{VCPDailyHot}}`: (由 V日报 插件提供) 提供全球热点新闻。包含从全球上百个门户网站聚合的热点摘要与URL链接。
*   `{{角色名日记本}}`: 特定角色（如 `小克`）的完整日记内容。数据来源于 `DailyNoteGet` 插件提供的 `{{AllCharacterDiariesData}}`。
*   `{{公共日记本}}`: 共享知识库的完整日记内容。数据来源于 `DailyNoteGet` 插件提供的 `{{AllCharacterDiariesData}}`。
*   `{{AllCharacterDiariesData}}`: (由 `DailyNoteGet` 插件提供) 一个 JSON 字符串，解析后为包含所有角色日记内容的对象。服务器内部使用此数据来支持 `{{角色名日记本}}` 的解析。
*   `{{xx表情包}}`: 特定表情包（如 `通用表情包`）的图片文件名列表 (以 `|` 分隔)。数据由 `EmojiListGenerator` 插件生成列表文件，服务器加载到内存缓存后提供。
*   `{{Port}}`: 服务器运行的端口号。
*   `{{Image_Key}}`: (由 `ImageServer` 插件配置提供) 图床服务的访问密钥。
*   `WhitelistImageModel`: (例如 `gemini-pro-vision`) 在此定义的模型将被特殊路由处理，用于图像生成等任务。
*   `WhitelistEmbeddingModel`: (例如 `text-embedding-ada-002`) 在此定义的模型将被特殊路由处理，用于向量化等任务，其请求和响应将被原样转发。
*   `{{Tar*}}`: (例如 `{{TarSysPrompt}}`, `{{TarEmojiPrompt}}`) 用户在 [`config.env`](config.env.example:1) 中定义的以 `Tar` 开头的自定义变量。这类变量拥有最高替换优先级，在所有其他占位符（包括 `{{Sar*}}`, `{{Var*}}`, 日期/时间等）之前被处理。其主要优势在于它们的值可以包含其他占位符，这些嵌套的占位符会在后续的替换阶段被进一步解析。这使得 `{{Tar*}}` 非常适合用于定义复杂和多层次的系统提示词模板。例如：`TarSysPrompt="今天是{{Date}}, 现在是{{Time}}, 天气{{VCPWeatherInfo}}。"`
*   `{{Var*}}`: (例如 `{{VarNeko}}`) 用户在 [`config.env`](config.env.example:1) 中定义的以 `Var` 开头的自定义变量。VCP 会按顺序对所有 `Var` 定义进行全局匹配和替换。如果多个 `Var` 定义匹配到同一文本，后定义的 `Var` 会覆盖先定义的 `Var`。因此，建议将较长或更精确的 `Var` 定义放在前面，较短或通用的 `Var` 定义放在后面，以确保预期的替换效果。例如，如果您定义了 `{{VarUser}}` 和 `{{VarUsername}}`，应将 `{{VarUsername}}` 定义在 `{{VarUser}}` 之前，以避免 `{{VarUsername}}` 被错误地替换为 `{{VarUser}}name`。
*   `{{Sar*}}`: (例如 `{{SarThink}}`, `{{SarVoice}}`) 针对特定模型的条件化变量。与 `Var*` 不同，`Sar*` 的注入逻辑由 `config.env` 中成对的 `SarModelX` 和 `SarPromptX` 变量控制（X为数字，如1, 2, 3...）。
    - **配置方式**:
        - `SarModelX=model-name-1,model-name-2`: 定义一个或多个模型名称（用逗号分隔），这些模型将共享同一个提示词。
        - `SarPromptX="提示词内容"`: 定义与 `SarModelX` 对应的提示词内容。
    - **工作机制**: 当服务器处理请求时，会检查当前请求的 `model` 是否在任何一个 `SarModelX` 列表中。
        - 如果找到匹配，服务器会将对应的 `SarPromptX` 的值替换掉上下文中的 **所有** `{{Sar*}}` 占位符（无论 `*` 是什么）。
        - 如果没有找到匹配，上下文中的所有 `{{Sar*}}` 占位符都会被移除（替换为空字符串）。
    - **应用场景**: 这允许为不同（或某几类）的 AI 模型注入完全不同的、定制化的指令或行为偏好，实现更精细的模型适配。例如，可以为一个模型注入思考链提示，为另一个模型注入语音交互优化提示。
*   `{{VCPAllTools}}`: 一个特殊的占位符，当被解析时，它会被替换为所有当前已加载且具有调用指令描述的 VCP 工具的完整描述和调用示例的集合。各个工具的描述之间会用分隔符隔开，方便AI全面了解可用工具。
*   `{{ShowBase64}}`: 当此占位符出现在用户消息或系统提示词中时，`ImageProcessor` 插件将被跳过，Base64数据将直接发送给Model。
*   `{{VCPWeaherInfo}}`: 由WeatherReporter提供的占位符，提供完整的天气预警，24小时精准天气，7日天气预报。
*   `{{ArxivDailyPapersData}}`: (由 `ArxivDailyPapers` 插件提供) 一个 JSON 字符串，包含从 Arxiv 获取的最新研究论文列表。
*   `{{CrossRefDailyPapersData}}`: (由 `CrossRefDailyPapers` 插件提供) 一个 JSON 字符串，包含从 CrossRef 获取的最新研究论文列表。
*   `{{VCP_ASYNC_RESULT::PluginName::RequestId}}`: (由异步插件在初始响应中引导AI使用，并由服务器动态替换) 这是一个特殊格式的占位符，用于在AI的对话上下文中“订阅”一个异步任务的最终结果。
    - `PluginName`: 异步插件的名称 (与 `plugin-manifest.json` 中的 `name` 一致)。
    - `RequestId`: 该异步任务的唯一ID (通常由插件在初始响应中提供，并用于回调和结果文件命名)。
    - **工作机制**: 当异步插件完成并通过回调将其结果保存到服务器的 `VCPAsyncResults/PluginName-RequestId.json` 文件后，`server.js` 在处理包含此占位符的文本时（例如，在AI生成回复前处理上下文，或在AI回复发送给用户前处理回复内容），会读取对应的结果文件。如果文件存在，占位符会被替换为文件中的实际结果信息（通常是回调JSON中的 `message` 字段或一个格式化的成功/失败摘要）。如果结果文件尚不存在（任务仍在处理中），占位符会被替换为一个“结果待更新”的提示。这使得AI能够在后续交互中“看到”或在其回复中体现异步任务的最终状态，实现了AI对异步结果的即时感知和自然融入对话流。


### 插件与配置驱动变量

### 用户自定义变量 (定义于 `config.env`)
- **`Agent{{*}}`**: 变量基座。其值可以包含富文本和其他占位符，实现绝对的模板定义。非常适合用于构建角色模板。其中的所有 `Tar`、`Var`/`Sar`等占位符都会被依次转译。
- **`{{Tar*}}`, `{{Var*}}`, `{{Sar*}}`**: 这些自定义变量现在支持从外部 `.txt` 文件加载内容，极大地增强了管理长文本、富文本和结构化提示词的能力。
  - **使用方法**: 在 `config.env` 中，将变量的值设置为一个 `.txt` 文件名，例如 `VarMyPrompt=my_prompt.txt`。
  - **文件位置**: 服务器会自动在根目录下的 `TVStxt/` 文件夹中寻找并读取该文件的内容作为变量值。
  - **嵌套解析**: 文件内容本身也可以包含其他 VCP 占位符，服务器会进行递归解析。
- **`{{Tar*}}`**: 最高优先级。其值可以包含其他占位符，实现复杂、多层次的模板定义。非常适合用于构建模块化的系统提示词。
- **`{{Var*}}`**: 通用自定义变量，按定义顺序进行全局匹配和替换。
- **`{{Sar*}}`**: 特殊自定义变量，通过 `SarModelX`/`SarPromptX` 对进行配置，其生效与否会根据当前使用的 AI 模型进行判断，允许为不同模型配置特定值。

## 13. 系统提示词工程：VCP 环境下的 AI 指挥艺术

在 VCP 环境下，系统提示词 (System Prompt) 不再仅仅是简单的角色扮演指令，它成为了指挥 AI Agent 如何感知世界、思考问题、运用工具、管理记忆、以及与其他 Agent 协作的“总纲领”和“行为逻辑注入器”。

以下是一个利用 VCP 特性（尤其是 `Tar*` 变量和插件占位符）构建模块化、动态化、功能强大的系统提示词的示例：

### 首先，在 `config.env` 中定义基础 `Tar*` 模块

```plaintext
# config.env 文件中的示例 Tar 变量定义
# Agent专用配置目录
AgentNova=Nova.txt
# 前置系统变量
TarSysPrompt="{{VarTimeNow}}当前地址是{{VarCity}},当前天气是{{VCPWeatherInfo}}。"
TarEmojiPrompt='本服务器支持表情包功能，通用表情包图床路径为{{VarHttpUrl}}:5890/pw={{Image_Key}}/images/通用表情包，注意[/通用表情包]路径指代，表情包列表为{{通用表情包}}，你可以灵活的在你的输出中插入表情包，调用方式为<img src="{{VarHttpUrl}}:5890/pw={{Image_Key}}/images/通用表情包/阿库娅-一脸智障.jpg" width="150">,使用Width参数来控制表情包尺寸（50-200）。'
# VCP类，可以使用{{VCPAllTools}}定义，亦可以自定义。
VarToolList="文生图工具{{VCPFluxGen}} 计算器工具{{VCPSciCalculator}},联网搜索工具{{VCPTavilySearch}},网页获取工具{{VCPUrlFetch}}；看b站视频工具{{VCPBilibiliFetch}}；使用Suno唱歌工具{{VCPSunoGen}},联络别的AI工具{{VCPAgentAssistant}},给用户的手机/电脑发消息工具{{AgentMessage}}。"
```

### 然后，在实际传递给 AI 模型的系统提示词中组合这些 `Tar*` 模块,以下是一个标准的系统提示词示例用于测试。

```plaintext
{{Nova}}
```

### 这种模块化、基于变量的系统提示词工程具有以下优势

- **清晰性与可维护性**: 将复杂的提示词分解为逻辑清晰的模块，易于理解、修改和扩展。
- **动态性与情境感知**: 通过动态变量，使 AI 的“初始认知”与当前真实环境和历史记忆保持一致。
- **能力全面注入**: 通过工具占位符，确保 AI 始终了解其可用的全部工具及其最新用法。
- **行为引导**: 通过精心设计的 `Tar*` 模块，可以精确引导 AI 的行为模式、沟通风格、以及与 VCP 各核心系统的交互方式。
- **高度可定制**: 用户可以根据具体需求，灵活组合或修改这些 `Tar*` 模块，快速定制出适用于不同场景、不同 AI Agent 的系统提示词。

精通 VCP 环境下的系统提示词工程，是释放 AI Agent 全部潜能、实现“AI 指挥艺术”的关键。

## 14. 未来展望：通往更高级 AI Agent 的路径

VCP 的征程远未结束，我们对未来充满期待，并已规划了更激动人心的发展方向：

- **增强的插件间协作与插件内工作流**: 实现插件间更高效的数据流转和事件通信，甚至在复杂插件内部构建“微型VCP”来编排子模块，支持更细粒度的自动化任务分解。 (已实现)
- **深化的 Agent 间自主通信与协同智能**: 建立标准化的 Agent 间通信总线 (ACB) 和协作原语，支持 AI Agent 动态组队、自主协商、角色分配，从“人类指挥-AI执行”演进为“AI自主团队协作”。 (已实现)
- **赋予 AI Agent 主动交互与实时通知能力**: 构建 VCP 内部事件总线与触发器，允许 AI Agent 在满足特定条件时（如日程提醒、任务完成、外部事件）主动向用户、其他 Agent 或外部系统发起通信或执行动作，从“被动响应者”进化为具有“主动服务意识”的智能伙伴。 (已实现 `AgentMessage` 和 `WebSocketServer`，为主动通知打下基础)
- **持续研发与实现“深度情境记忆回溯机制”**: 如 `VCP.md` 中所述，通过“日记条目指纹匹配完整聊天历史”并结合“AI 中间件与信息传播链分析”，实现 AI 对其记忆产生时的完整背景和微妙上下文的“深度回溯”理解。 
- **构建繁荣的插件生态与开发者社区**: 完善文档、API、工具，积极建设社区，吸引更多开发者共同扩展 VCP 的能力边界。
- **对 AI 伦理、安全与可控性的不懈追求**: 随着 AI Agent 自主行动能力的增强，我们将持续投入研究更全面的权限管理、行为审计、风险评估和应急干预机制。

我们坚信，VCP 所代表的设计哲学和技术路径，正引领着一条通往更智能、更自主、更具适应性和协作性的高级 AI Agent 未来的光明大道。VCP 不仅是一个技术框架，更是一个旨在释放 AI 无限潜能、并最终为人类社会发展做出独特贡献的孵化器。

## 15. 许可证 (License)

本项目采用 **Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International (CC BY-NC-SA 4.0)** 许可证。

简单来说，这意味着您可以：

- **共享** — 在任何媒介以任何形式复制、发行本作品。
- **演绎** — 修改、转换或以本作品为基础进行创作。只要你遵守许可协议条款，许可人就无法收回你的这些权利。

惟须遵守下列条件：

- **署名 (BY)** — 您必须给出适当的署名，提供指向本许可的链接，同时标明是否（对原始作品）作了修改。您可以用任何合理的方式来署名，但是不得以任何方式暗示许可人为您或您的使用背书。
- **非商业性使用 (NC)** — 您不得将本作品用于商业目的。
- **相同方式共享 (SA)** — 如果您再混合、转换或者基于本作品进行创作，您必须基于与原先许可协议相同的许可协议分发您贡献的作品。

详情请参阅 `LICENSE` 文件。

## 16. 免责声明与使用限制

- **开发阶段**: 本 VCP 工具箱项目目前仍处于积极开发阶段，可能存在未知错误、缺陷或不完整功能。
- **按原样提供**: 本项目按“原样”和“可用”状态提供，不附带任何形式的明示或暗示保证。
- **风险自负**: 使用本项目的风险完全由您自行承担。开发者不承担任何因使用或无法使用本项目而导致的直接或间接损害。
- **无商业化授权**: 明确禁止将本项目及其衍生作品用于任何主要的商业目的。
- **API 使用成本**: 请注意，本项目集成的部分插件依赖于可能产生费用的第三方 API 服务。您有责任了解并承担这些成本。
- **安全责任**: 请妥善保管您的 API 密钥，切勿硬编码或提交到公共代码库。
- **隐私信息**: 再次强调，请勿使用非官方的 API 代理商或反代类 API 供应商运行本项目，以防 AI 笔记系统中的敏感信息泄露！

我们相信， VCP 将为 AI 应用的开发带来前所未有的灵活性和可能性。欢迎一切形式的贡献、反馈与交流，让我们共同探索 AI 的未来！

[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/lioensky/VCPToolBox)
