{"manifestVersion": "1.0.0", "name": "PluginManagerCenter", "displayName": "插件管理中心", "version": "1.0.0", "description": "集中管理VCP插件的状态、配置和性能监控", "author": "VCP Developer", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node PluginManagerCenter.js"}, "communication": {"protocol": "stdio", "timeout": 30000}, "configSchema": {"ENABLE_PERFORMANCE_MONITORING": {"type": "boolean", "default": true, "description": "是否启用插件性能监控"}, "AUTO_CHECK_DEPENDENCIES": {"type": "boolean", "default": true, "description": "是否自动检查插件依赖"}, "STATS_RETENTION_DAYS": {"type": "integer", "default": 30, "description": "性能统计数据保留天数"}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "ListPlugins", "description": "列出所有插件及其状态信息。支持以下操作：\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent署名「末」\ntool_name:「始」PluginManagerCenter「末」\ncommand:「始」list_plugins「末」\nfilter:「始」(可选) 过滤条件：all/enabled/disabled/error「末」\ndetailed:「始」(可选) 是否显示详细信息：true/false，默认false「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回插件列表，包括名称、状态、版本、最后调用时间等信息。", "example": "查看所有插件状态"}, {"commandIdentifier": "Plugin<PERSON><PERSON><PERSON>", "description": "查看特定插件的详细状态和配置信息。\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent署名「末」\ntool_name:「始」PluginManagerCenter「末」\ncommand:「始」plugin_status「末」\nplugin_name:「始」插件名称「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回指定插件的详细信息，包括配置、依赖、性能统计等。", "example": "查看SciCalculator插件状态"}, {"commandIdentifier": "TogglePlugin", "description": "启用或禁用指定插件。\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent署名「末」\ntool_name:「始」PluginManagerCenter「末」\ncommand:「始」toggle_plugin「末」\nplugin_name:「始」插件名称「末」\naction:「始」enable/disable「末」\n<<<[END_TOOL_REQUEST]>>>\n\n动态启用或禁用插件，无需重启服务。", "example": "禁用某个有问题的插件"}, {"commandIdentifier": "PluginStats", "description": "查看插件性能统计信息。\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent署名「末」\ntool_name:「始」PluginManagerCenter「末」\ncommand:「始」plugin_stats「末」\nplugin_name:「始」(可选) 特定插件名称，不填则显示所有「末」\ntime_range:「始」(可选) 时间范围：1d/7d/30d，默认7d「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回插件调用统计，包括调用次数、平均耗时、成功率等。", "example": "查看过去7天的插件使用统计"}, {"commandIdentifier": "CheckDependencies", "description": "检查插件依赖是否满足。\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent署名「末」\ntool_name:「始」PluginManagerCenter「末」\ncommand:「始」check_dependencies「末」\nplugin_name:「始」(可选) 特定插件名称，不填则检查所有「末」\nfix_missing:「始」(可选) 是否尝试自动修复：true/false，默认false「末」\n<<<[END_TOOL_REQUEST]>>>\n\n检查并报告插件依赖状态，可选择自动安装缺失依赖。", "example": "检查所有插件的依赖状态"}, {"commandIdentifier": "PluginConfig", "description": "管理插件配置。\n\n<<<[TOOL_REQUEST]>>>\nmaid:「始」Agent署名「末」\ntool_name:「始」PluginManagerCenter「末」\ncommand:「始」plugin_config「末」\nplugin_name:「始」插件名称「末」\naction:「始」get/set/reset「末」\nconfig_key:「始」(set时必需) 配置键名「末」\nconfig_value:「始」(set时必需) 配置值「末」\n<<<[END_TOOL_REQUEST]>>>\n\n获取、设置或重置插件配置。支持动态配置更新。", "example": "修改插件配置参数"}]}}